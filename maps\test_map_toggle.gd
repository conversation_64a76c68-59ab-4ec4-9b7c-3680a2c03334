extends Node2D

# Test scene để validate MapScene toggle system
# Chạy scene này và nhấn M để test

@onready var label = $UI/TestLabel

func _ready():
	print("🧪 MapScene Toggle Test Scene đã sẵn sàng")
	print("📋 Hướng dẫn test:")
	print("1. Nhấn M để mở MapScene")
	print("2. Trong MapScene, nhấn M để quay về")
	print("3. Test nhiều lần để kiểm tra stability")
	
	# Update UI
	if label:
		label.text = "MapScene Toggle Test\n\nNhấn M để test toggle MapScene\nCheck console để xem debug info"

func _input(event):
	if event.is_action_pressed("ui_accept"): # Enter key
		print("ℹ️ Test scene đang hoạt động, MapToggleManager sẽ xử lý phím Enter")
		# Kiểm tra scene_file_path tồn tại với null safety
		var path = "Unknown scene"
		if self and has_method("get") and "scene_file_path" in self:
			path = scene_file_path if scene_file_path else "No path"
		elif get_tree() and get_tree().current_scene:
			var current_scene = get_tree().current_scene
			if current_scene and is_instance_valid(current_scene) and current_scene.scene_file_path:
				path = current_scene.scene_file_path
		print("ℹ️ Scene hiện tại: %s" % path)
