# Complete System Validation Test
# Test tất cả hệ thống: teleport, UI, input mapping
extends Node2D

func _ready():
	print("🧪 === COMPLETE SYSTEM VALIDATION TEST ===")
	await get_tree().process_frame
	await get_tree().process_frame
	
	test_all_systems()

func test_all_systems():
	print("\n🧪 Starting comprehensive system test...")
	
	test_autoload_systems()
	test_input_mappings()
	test_teleport_positions()
	test_scene_files()
	test_map_name_ui()
	test_teleport_gates()

func test_autoload_systems():
	print("\n📦 1. TESTING AUTOLOAD SYSTEMS:")
	
	var autoloads = [
		["SceneManager", SceneManager],
		["TeleportPositionMapping", TeleportPositionMapping],
		["GlobalMapNameUI", GlobalMapNameUI],
		["MapToggleManager", MapToggleManager]
	]
	
	for autoload_info in autoloads:
		var autoload_name = autoload_info[0]
		var reference = autoload_info[1]
		
		if reference:
			print("✅ %s: Available" % autoload_name)
		else:
			print("❌ %s: NOT AVAILABLE" % autoload_name)

func test_input_mappings():
	print("\n⌨️ 2. TESTING INPUT MAPPINGS:")
	
	var inputs_to_test = [
		"teleport_interact",
		"ui_accept", 
		"toggle_map"
	]
	
	for input_name in inputs_to_test:
		if InputMap.has_action(input_name):
			print("✅ %s: Mapped" % input_name)
			var events = InputMap.action_get_events(input_name)
			for event in events:
				if event is InputEventKey:
					print("   Key: %s (code: %s)" % [_get_key_name(event.physical_keycode), event.physical_keycode])
		else:
			print("❌ %s: NOT MAPPED" % input_name)

func test_teleport_positions():
	print("\n🎯 3. TESTING TELEPORT POSITIONS:")
	
	if not TeleportPositionMapping:
		print("❌ TeleportPositionMapping not available")
		return
	
	var test_routes = [
		["dong_dau", "doi_tre"],
		["dong_dau", "lang_van_lang"],
		["lang_van_lang", "hang_an"],
		["hang_an", "lang_van_lang"]
	]
	
	for route in test_routes:
		var from_map = route[0]
		var to_map = route[1]
		var spawn_position = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		
		if spawn_position != Vector2.ZERO:
			print("✅ %s → %s: %s" % [from_map, to_map, spawn_position])
		else:
			print("❌ %s → %s: Invalid position" % [from_map, to_map])

func test_scene_files():
	print("\n📁 4. TESTING SCENE FILES:")
	
	var critical_scenes = [
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn", 
		"res://maps/doi_tre/scenes/doi_tre.tscn",
		"res://maps/hang_an/scenes/hang_an.tscn",
		"res://maps/New_loor/scenes/MapScene.tscn"
	]
	
	for scene_path in critical_scenes:
		if ResourceLoader.exists(scene_path):
			var resource = ResourceLoader.load(scene_path)
			if resource:
				print("✅ %s: Exists and loadable" % scene_path.get_file())
			else:
				print("⚠️ %s: Exists but cannot load" % scene_path.get_file())
		else:
			print("❌ %s: Does not exist" % scene_path.get_file())

func test_map_name_ui():
	print("\n🗺️ 5. TESTING MAP NAME UI:")
	
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI available")
		
		# Test if UI is visible
		if GlobalMapNameUI.is_map_name_visible():
			print("✅ Map name UI is visible")
			var current_name = GlobalMapNameUI.get_current_map_name()
			print("   Current map name: '%s'" % current_name)
		else:
			print("⚠️ Map name UI is not visible")
			print("🔧 Attempting to recreate UI...")
			GlobalMapNameUI._create_persistent_map_ui()
			GlobalMapNameUI.set_map_name("Test Map")
	else:
		print("❌ GlobalMapNameUI not available")

func test_teleport_gates():
	print("\n🚪 6. TESTING TELEPORT GATES:")
	
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates in current scene" % gates.size())
	
	for gate in gates:
		if gate is TeleportGate:
			print("🚪 Gate: %s" % gate.gate_id)
			print("   Target: %s" % gate.target_scene)
			print("   Position: %s" % gate.target_position)
			
			# Test target scene
			if not gate.target_scene.is_empty():
				if ResourceLoader.exists(gate.target_scene):
					print("   ✅ Target scene exists")
				else:
					print("   ❌ Target scene missing")
			else:
				print("   ⚠️ No target scene set")

func _get_key_name(keycode: int) -> String:
	"""Get human-readable key name"""
	match keycode:
		4194309:
			return "Enter"
		77:
			return "M"
		32:
			return "Space"
		_:
			return "Key_%d" % keycode

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running all system tests...")
		test_all_systems()
