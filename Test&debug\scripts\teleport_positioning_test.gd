# Teleport Positioning System Test
# Tests the enhanced teleport gate positioning system
extends Node2D

func _ready():
	print("🧪 === TELEPORT POSITIONING TEST ===")
	await get_tree().process_frame
	await get_tree().process_frame
	
	test_teleport_position_mapping()
	test_teleport_gates()
	test_specific_routes()
	print("🎯 === TEST COMPLETED ===")

func test_teleport_position_mapping():
	print("\n📍 1. TESTING TELEPORT POSITION MAPPING SYSTEM")
	
	if not TeleportPositionMapping:
		print("❌ TeleportPositionMapping not available!")
		return
	
	print("✅ TeleportPositionMapping is available")
	
	# Test some key routes
	var test_routes = [
		["lang_van_lang", "dong_dau"],
		["dong_dau", "lang_van_lang"],
		["lang_van_lang", "rung_nuong"],
		["rung_nuong", "lang_van_lang"],
		["dong_dau", "doi_tre"],
		["doi_tre", "dong_dau"]
	]
	
	for route in test_routes:
		var from_map = route[0]
		var to_map = route[1]
		var spawn_position = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		
		if spawn_position != Vector2.ZERO:
			print("✅ %s → %s: %s" % [from_map, to_map, spawn_position])
		else:
			print("❌ %s → %s: NO POSITION FOUND" % [from_map, to_map])

func test_teleport_gates():
	print("\n🚪 2. TESTING TELEPORT GATES IN SCENE")
	
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates" % gates.size())
	
	if gates.size() == 0:
		print("⚠️ No teleport gates found in current scene")
		return
	
	for gate in gates:
		if gate is TeleportGate:
			print("\n🔍 Testing gate: %s" % gate.name)
			print("   Gate ID: %s" % gate.gate_id)
			print("   Target Scene: %s" % gate.target_scene)
			print("   Target Position: %s" % gate.target_position)
			
			# Test validation
			if gate.has_method("validate_teleport_route"):
				var is_valid = gate.validate_teleport_route()
				print("   Validation: %s" % ("✅ PASS" if is_valid else "❌ FAIL"))
			else:
				print("   ⚠️ validate_teleport_route method not found")

func test_specific_routes():
	print("\n🎯 3. TESTING SPECIFIC CRITICAL ROUTES")
	
	# Test the key issue: lang_van_lang → dong_dau
	print("\n🔍 Testing: lang_van_lang → dong_dau")
	if TeleportPositionMapping:
		TeleportPositionMapping.debug_route("lang_van_lang", "dong_dau")
		
		# Test reverse route
		print("🔍 Testing: dong_dau → lang_van_lang")
		TeleportPositionMapping.debug_route("dong_dau", "lang_van_lang")
		
		# Test a few more critical routes
		print("🔍 Testing: dong_dau → doi_tre")
		TeleportPositionMapping.debug_route("dong_dau", "doi_tre")
		
		print("🔍 Testing: doi_tre → dong_dau")
		TeleportPositionMapping.debug_route("doi_tre", "dong_dau")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running teleport positioning test...")
		test_teleport_position_mapping()
		test_teleport_gates()
		test_specific_routes()
