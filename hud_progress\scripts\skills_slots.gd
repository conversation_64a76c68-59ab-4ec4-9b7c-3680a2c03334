extends Control

# ----- Properties -----
# UI references
var skill_buttons: Array = []
var cooldown_bars: Array = []
var original_positions: Array = []

# Skill timing properties
var skill_cooldowns: Array = [2.0, 2.0, 6.0, 4.0, 2.0, 2.0, 2.0] # Cooldown for each skill
var cooldown_timers: Array = []

# Skill mana costs
var skill_mana_costs: Array = [10, 20, 15, 25]  # Mana cost for each skill

# Skill icons
var skill_icons = [
	preload("res://abilities/resources/swordman/attack_1.tres"),
	preload("res://abilities/resources/swordman/attack_2.tres"),
	preload("res://abilities/resources/swordman/attack_3.tres"),
	preload("res://abilities/resources/swordman/attack_4.tres"),
]

# Potion and mana slot icons
var potion_icon = preload("res://Items/Potion/Health_Potion.tres")
var mana_icon = preload("res://Items/Potion/Mana_Potion.tres")

var _character: Player = null

var health_potion_count: int = 3  # Số lượng health potion ban đầu
var mana_potion_count: int = 3    # Số lượng mana potion ban đầu

var health_potion: Potion = preload("res://Items/Potion/Health_Potion.tres")
var mana_potion: Potion = preload("res://Items/Potion/Mana_Potion.tres")



# ----- Constants -----
const SKILL_COUNT = 4
const SKILL_HOTKEYS = {
	"skill_1": KEY_1,  # Phím 1
	"skill_2": KEY_2,  # Phím 2
	"skill_3": KEY_3,  # Phím 3
	"skill_4": KEY_4,   # Phím 4
	"health_potion": KEY_6,  # Phím 6 cho health potion
	"mana_potion": KEY_7     # Phím 7 cho mana potion
}

# ----- Lifecycle Methods -----
func _ready() -> void:
	print("Starting initialization...")

	# Thứ tự khởi tạo là quan trọng
	initialize_arrays()

	# Tìm player node trước khi thiết lập UI
	var player = get_tree().get_root().find_child("Player", true, false)
	if player != null:
		print("Player found: ", player)
		initialize(player)
	else:
		push_error("Error: Player node not found in scene tree")

	# Sau đó thiết lập UI
	setup_skill_buttons()
	setup_skill_icons()
	setup_cooldown_bars()
	setup_utility_slots()



	_check_button_connections()
	_check_button_types()  # Kiểm tra loại nút và khả năng nhận input

	print("Initialization complete")
	print_tree()

func _process(delta: float) -> void:
	if _character == null:
		return
	update_cooldowns(delta)

# ----- Initialization Methods -----
func initialize_arrays() -> void:
	skill_buttons.resize(SKILL_COUNT)
	cooldown_bars.resize(SKILL_COUNT)
	cooldown_timers.resize(SKILL_COUNT)
	original_positions.resize(SKILL_COUNT)

	# Initialize cooldown timers and positions
	for i in range(SKILL_COUNT):
		cooldown_timers[i] = 0.0
		original_positions[i] = Vector2.ZERO

func setup_skill_buttons() -> void:
	for i in range(SKILL_COUNT):
		var button_path = "Skill_" + str(i + 1)

		# Kiểm tra xem nút đã tồn tại chưa
		var existing_button = get_node_or_null(button_path)

		if existing_button == null:
			# Tạo nút nếu nó chưa tồn tại
			var new_button = Button.new()
			new_button.name = "Skill_" + str(i + 1)
			new_button.custom_minimum_size = Vector2(40, 40)
			new_button.focus_mode = Control.FOCUS_ALL
			new_button.mouse_filter = Control.MOUSE_FILTER_STOP
			add_child(new_button)
			skill_buttons[i] = new_button
			print("Created new button: ", button_path)
		else:
			# Nếu nút đã tồn tại, đảm bảo nó có thể nhận input
			skill_buttons[i] = existing_button

			# Đặt thuộc tính để chắc chắn nút hoạt động đúng
			skill_buttons[i].focus_mode = Control.FOCUS_ALL
			skill_buttons[i].mouse_filter = Control.MOUSE_FILTER_STOP

			print("Found existing button: ", button_path)

		# Lưu vị trí gốc
		original_positions[i] = skill_buttons[i].position

		# Hủy kết nối tín hiệu cũ nếu có để tránh kết nối nhiều lần
		if skill_buttons[i].pressed.is_connected(Callable(self, "_on_skill_button_pressed").bind(i)):
			skill_buttons[i].pressed.disconnect(Callable(self, "_on_skill_button_pressed").bind(i))

		# Kết nối tín hiệu pressed với phương thức xử lý
		skill_buttons[i].pressed.connect(_on_skill_button_pressed.bind(i))

		print("Connected signal for button: ", button_path)

		# Kết nối các signal chuột
		if not skill_buttons[i].mouse_entered.is_connected(_on_skill_mouse_entered.bind(i)):
			skill_buttons[i].mouse_entered.connect(_on_skill_mouse_entered.bind(i))
		if not skill_buttons[i].mouse_exited.is_connected(_on_skill_mouse_exited.bind(i)):
			skill_buttons[i].mouse_exited.connect(_on_skill_mouse_exited.bind(i))

		print("Connected mouse signals for button: ", button_path)

func setup_cooldown_bars() -> void:
	for i in range(SKILL_COUNT):
		# Lấy reference đến TextureProgressBar từ scene tree
		var cooldown_bar = get_node_or_null("Skill_" + str(i + 1) + "/CooldownBar_" + str(i + 1))
		if cooldown_bar != null:
			cooldown_bars[i] = cooldown_bar
			# Thiết lập các thuộc tính của TextureProgressBar
			cooldown_bars[i].min_value = 0
			cooldown_bars[i].max_value = 100
			cooldown_bars[i].value = 0
			cooldown_bars[i].visible = false
			print("Found and connected to CooldownBar_" + str(i + 1))
		else:
			push_error("Could not find CooldownBar_" + str(i + 1))

func setup_skill_icons() -> void:
	for i in range(SKILL_COUNT):
		if skill_buttons[i] != null and is_instance_valid(skill_buttons[i]) and skill_icons[i] != null:
			# Kiểm tra xem icon đã tồn tại chưa
			var existing_icon = null
			# Null check trước khi gọi get_children()
			if skill_buttons[i] != null:
				for child in skill_buttons[i].get_children():
					if child is TextureRect:
						existing_icon = child
						break

			if existing_icon != null:
				# Cập nhật icon nếu đã tồn tại
				existing_icon.texture = skill_icons[i]
				print("Updated existing icon for Skill_" + str(i + 1))
			else:
				# Tạo mới nếu chưa có
				var icon = TextureRect.new()
				icon.texture = skill_icons[i]
				icon.expand_mode = TextureRect.EXPAND_KEEP_SIZE
				icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
				icon.custom_minimum_size = Vector2(40, 40)
				icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
				icon.size_flags_vertical = Control.SIZE_SHRINK_CENTER

				# Quan trọng: TextureRect không được chặn input của button
				icon.mouse_filter = Control.MOUSE_FILTER_IGNORE

				skill_buttons[i].add_child(icon)
				print("Created new icon for Skill_" + str(i + 1))
# ----- Cooldown Management -----
func update_cooldowns(delta: float) -> void:
	for i in range(cooldown_timers.size()):
		if cooldown_timers[i] > 0:
			# Update timer
			cooldown_timers[i] = max(0, cooldown_timers[i] - delta)

			# Update cooldown bar with smoother progression
			if cooldown_bars[i] != null:
				# Tween the progress bar value for smoother animation
				var progress = (1.0 - (cooldown_timers[i] / skill_cooldowns[i])) * 100
				var tween = create_tween()
				tween.tween_property(cooldown_bars[i], "value", progress, 0.1)
				cooldown_bars[i].visible = true

			# Reset when cooldown is complete with fade out effect
			if cooldown_timers[i] <= 0:
				if cooldown_bars[i] != null:
					var fade_tween = create_tween()
					fade_tween.tween_property(cooldown_bars[i], "modulate", Color(1, 1, 1, 0), 0.2)
					fade_tween.tween_callback(func(): reset_skill_button(i))

func reset_skill_button(index: int) -> void:
	if skill_buttons[index] != null:
		skill_buttons[index].disabled = false
		skill_buttons[index].modulate = Color(1, 1, 1, 1)
		if cooldown_bars[index] != null:
			cooldown_bars[index].visible = false
			# Reset modulate của cooldown bar về 1
			cooldown_bars[index].modulate = Color(1, 1, 1, 1)
			# Reset giá trị về 0
			cooldown_bars[index].value = 0
		print("Skill button ", index, " reset and enabled")

# ----- Button Events -----
func _on_skill_button_pressed(skill_index: int) -> void:
	print("Skill button " + str(skill_index) + " pressed!")  # Debug log rõ ràng

	if _character == null:
		push_error("Error: _character is null. Did you forget to call initialize()?")
		return

	if skill_buttons[skill_index] == null:
		push_error("Error: Skill button is null for index " + str(skill_index))
		return

	if skill_buttons[skill_index].disabled:
		print("Skill button ", skill_index, " is disabled")
		return

	print("Current mana: ", _character.mana, ", Required mana: ", skill_mana_costs[skill_index])

	if _character.mana >= skill_mana_costs[skill_index]:
		print("Activated skill " + str(skill_index + 1))

		_character.mana -= skill_mana_costs[skill_index]
		update_mana_ui()

		# Start cooldown
		cooldown_timers[skill_index] = skill_cooldowns[skill_index]

		# Update button state
		skill_buttons[skill_index].disabled = true
		skill_buttons[skill_index].modulate = Color(1, 1, 1, 0.5)

		# Update cooldown bar
		if cooldown_bars[skill_index] != null:
			cooldown_bars[skill_index].modulate = Color(1, 1, 1, 1) # Đặt opacity về 1
			cooldown_bars[skill_index].value = 0  # Bắt đầu từ 0
			cooldown_bars[skill_index].visible = true
			print("Starting cooldown for skill ", skill_index)

		# Animate button
		animate_button_press(skill_index)

		# Thêm visual feedback khi skill được kích hoạt
		if skill_buttons[skill_index] != null:
			var flash_tween = create_tween()
			flash_tween.tween_property(skill_buttons[skill_index], "modulate",
				Color(1.5, 1.5, 1.5, 1), 0.1)
			flash_tween.tween_property(skill_buttons[skill_index], "modulate",
				Color(1, 1, 1, 1), 0.1)
	else:
		print("Not enough mana to activate skill " + str(skill_index + 1))

func update_mana_ui():
	var hud = get_node("/root/LangVanLang/Player/CanvasLayer/HUD")
	var mana_bar = hud.get_node("ManaProgressBar")
	mana_bar.value = _character.mana



func animate_button_press(skill_index: int) -> void:
	var button = skill_buttons[skill_index]
	if button == null:
		return

	var tween = create_tween()
	var original_pos = original_positions[skill_index]

	tween.tween_property(button, "position", original_pos + Vector2(5, 0), 0.1)
	tween.tween_property(button, "position", original_pos, 0.1)

func initialize(character: Player) -> void:
	_character = character
	if _character == null:
		push_error("Error: _character is null in initialize()")
	else:
		print("Character initialized successfully: ", _character)

func _check_button_connections() -> void:
	for i in range(SKILL_COUNT):
		if skill_buttons[i] != null:
			var connections = skill_buttons[i].pressed.get_connections()
			print("Button ", i, " has ", connections.size(), " signal connection(s)")
			for connection in connections:
				print("  - Connected to: ", connection.callable)

func _check_button_types() -> void:
	for i in range(SKILL_COUNT):
		if skill_buttons[i] != null:
			print("Button ", i, " is of type: ", skill_buttons[i].get_class())
			print("Can receive input? ", skill_buttons[i].mouse_filter == Control.MOUSE_FILTER_STOP)

func _input(event: InputEvent) -> void:
	# Chỉ xử lý phím tắt
	if event is InputEventKey and event.pressed:
		match event.keycode:
			SKILL_HOTKEYS["skill_1"]:
				_on_skill_button_pressed(0)
			SKILL_HOTKEYS["skill_2"]:
				_on_skill_button_pressed(1)
			SKILL_HOTKEYS["skill_3"]:
				_on_skill_button_pressed(2)
			SKILL_HOTKEYS["skill_4"]:
				_on_skill_button_pressed(3)
			SKILL_HOTKEYS["health_potion"]:
				_on_health_potion_pressed()
			SKILL_HOTKEYS["mana_potion"]:
				_on_mana_potion_pressed()

func _on_health_potion_pressed() -> void:
	if _character == null:
		return

	if health_potion_count <= 0:
		print("No health potions remaining!")
		return

	# Sử dụng health potion với amount từ resource
	_character.health += health_potion.amount
	health_potion_count -= 1
	update_health_ui()

	# Cập nhật visual
	update_potion_display("health")
	print("Health potion used. Restored: ", health_potion.amount, " HP. Remaining: ", health_potion_count)

func update_health_ui():
	var hud = get_node("/root/LangVanLang/Player/CanvasLayer/HUD")
	var mana_bar = hud.get_node("HealthProgressBar")
	mana_bar.value = _character.health

func _on_mana_potion_pressed() -> void:
	if _character == null:
		return

	if mana_potion_count <= 0:
		print("No mana potions remaining!")
		return

	# Sử dụng mana potion với amount từ resource
	_character.mana += mana_potion.amount
	mana_potion_count -= 1

	# Cập nhật visual
	update_mana_ui()
	update_potion_display("mana")
	print("Mana potion used. Restored: ", mana_potion.amount, " MP. Remaining: ", mana_potion_count)

# Thêm hàm mới để cập nhật hiển thị số lượng potion
func update_potion_display(potion_type: String) -> void:
	var slot = null
	var count = 0

	if potion_type == "health":
		slot = get_node_or_null("Potion_Slot_1")
		count = health_potion_count
	else:
		slot = get_node_or_null("Potion_Slot_2")
		count = mana_potion_count

	if slot:
		# Cập nhật opacity nếu hết potion
		slot.modulate.a = 1.0 if count > 0 else 0.5

		# Cập nhật label hiển thị số lượng
		var count_label = slot.get_node_or_null("CountLabel")
		if count_label == null:
			count_label = Label.new()
			count_label.name = "CountLabel"
			count_label.position = Vector2(90, 90)  # Vị trí ở góc dưới phải của slot
			count_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
			count_label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
			count_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))  # Màu trắng
			count_label.add_theme_font_size_override("font_size", 24)  # Kích thước font lớn hơn
			count_label.add_theme_constant_override("shadow_offset_x", 1)  # Đổ bóng
			count_label.add_theme_constant_override("shadow_offset_y", 1)
			count_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.5))  # Màu đổ bóng
			slot.add_child(count_label)

		count_label.text = str(count)

func setup_utility_slots() -> void:
	# Thiết lập slot bình máu
	var health_slot = get_node_or_null("Potion_Slot_1")
	if health_slot != null:
		# Cập nhật label số lượng
		var count_label = health_slot.get_node_or_null("CountLabel")
		if count_label == null:
			count_label = Label.new()
			count_label.name = "CountLabel"
			count_label.position = Vector2(90, 90)  # Vị trí ở góc dưới phải của slot
			count_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
			count_label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
			count_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))  # Màu trắng
			count_label.add_theme_font_size_override("font_size", 24)  # Kích thước font lớn hơn
			count_label.add_theme_constant_override("shadow_offset_x", 1)  # Đổ bóng
			count_label.add_theme_constant_override("shadow_offset_y", 1)
			count_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.5))  # Màu đổ bóng
			health_slot.add_child(count_label)

		count_label.text = str(health_potion_count)

		# Kết nối signal nếu chưa được kết nối
		if not health_slot.pressed.is_connected(_on_health_potion_pressed):
			health_slot.pressed.connect(_on_health_potion_pressed)
	else:
		push_error("Health potion slot (Potion_Slot_1) not found")

	# Thiết lập slot bình mana
	var mana_slot = get_node_or_null("Potion_Slot_2")
	if mana_slot != null:
		# Cập nhật label số lượng
		var count_label = mana_slot.get_node_or_null("CountLabel")
		if count_label == null:
			count_label = Label.new()
			count_label.name = "CountLabel"
			count_label.position = Vector2(90, 90)  # Vị trí ở góc dưới phải của slot
			count_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
			count_label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
			count_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))  # Màu trắng
			count_label.add_theme_font_size_override("font_size", 24)  # Kích thước font lớn hơn
			count_label.add_theme_constant_override("shadow_offset_x", 1)  # Đổ bóng
			count_label.add_theme_constant_override("shadow_offset_y", 1)
			count_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.5))  # Màu đổ bóng
			mana_slot.add_child(count_label)

		count_label.text = str(mana_potion_count)

		# Kết nối signal nếu chưa được kết nối
		if not mana_slot.pressed.is_connected(_on_mana_potion_pressed):
			mana_slot.pressed.connect(_on_mana_potion_pressed)
	else:
		push_error("Mana potion slot (Potion_Slot_2) not found")

# Thêm các hàm xử lý sự kiện chuột
func _on_skill_mouse_entered(skill_index: int) -> void:
	if skill_buttons[skill_index] != null:
		# Hiệu ứng hover
		skill_buttons[skill_index].modulate = Color(1.2, 1.2, 1.2, 1)
		# Hiển thị tooltip nếu cần
		print("Mouse entered skill ", skill_index + 1)

func _on_skill_mouse_exited(skill_index: int) -> void:
	if skill_buttons[skill_index] != null:
		# Reset hiệu ứng hover
		skill_buttons[skill_index].modulate = Color(1, 1, 1, 1)
		# Ẩn tooltip nếu có
		print("Mouse exited skill ", skill_index + 1)
