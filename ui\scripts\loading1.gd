extends Control

# This is the initial scene that loads and immediately triggers the loading screen

func _ready():
    # Start loading the main scene immediately
    call_deferred("_load_main_scene")
    
func _load_main_scene():
    # Wait two frames to ensure everything is initialized
    await get_tree().process_frame
    await get_tree().process_frame
    
    # Start loading the main game scene
    SceneManager.goto_scene("res://maps/lang_van_lang/scenes/lang_van_lang.tscn") 