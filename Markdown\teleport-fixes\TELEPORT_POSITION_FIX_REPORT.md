# Teleport Position Fix Report

## 🎯 Vấn đề
Khi người chơi bước vào một cổng dịch chuyển (ví dụ: lang_van_lang → dong_dau), sau khi chuyển scene, Player không được đặt đúng vị trí tương ứng với cổng lang_van_lang trong map dong_dau.

## 🔧 Phân tích
Sau khi phân tích mã nguồn, tôi đã tìm thấy các vấn đề sau:

1. Trong file `player.gd`, hệ thống đang tìm kiếm một node `Global` và sử dụng thuộc tính `next_spawn_name` không tồn tại, trong khi hệ thống thực tế sử dụng `SceneManager` và các phương thức của nó.

2. <PERSON>ệ thống thực tế đã được thiết kế tốt với:
   - `SceneManager`: <PERSON><PERSON><PERSON><PERSON> lý việc chuyển scene và lưu trữ vị trí spawn
   - `TeleportPositionMapping`: <PERSON><PERSON><PERSON> trữ mapping giữa các cổng dịch chuyển
   - `teleport_gate.gd`: Quản lý cổng dịch chuyển và gọi đến SceneManager

3. Hệ thống đang hoạt động nhưng bị mâu thuẫn giữa các phương pháp cũ/mới trong `player.gd`

## 🔍 Giải pháp

1. Cập nhật file `player.gd` để sử dụng SceneManager thay vì Global
2. Đảm bảo quá trình đọc vị trí spawn từ SceneManager được thực hiện chính xác
3. Thêm log để dễ dàng debug vấn đề

## 📝 Các thay đổi chi tiết

### 1. Fix trong player.gd

```gdscript
# Mã cũ (không hoạt động)
var global_node = get_node_or_null("/root/Global")
if global_node and global_node.next_spawn_name != "":
    var spawn_node = get_node_or_null("/root/CurrentScene/" + global_node.next_spawn_name)
    if spawn_node:
        global_position = spawn_node.global_position
    else:
        print("⚠️ Không tìm thấy spawn point tên:", global_node.next_spawn_name)
    
    # Reset lại để tránh lỗi lần sau
    global_node.next_spawn_name = ""
```

```gdscript
# Mã mới (đã sửa)
# ⬇️ Thiết lập vị trí Player dựa theo SceneManager sau khi teleport
if SceneManager and SceneManager.has_method("has_next_spawn_position") and SceneManager.has_next_spawn_position():
    var target_pos = SceneManager.get_next_spawn_position()
    if target_pos != Vector2.ZERO:
        global_position = target_pos
        print("🎯 Player đã được teleport đến vị trí: %s" % global_position)
        
        # Clear vị trí để tránh sử dụng lại khi chuyển scene khác
        if SceneManager.has_method("clear_next_spawn_position"):
            SceneManager.clear_next_spawn_position()
    else:
        print("⚠️ Vị trí teleport không hợp lệ (zero)")
else:
    print("📍 Không có vị trí teleport được thiết lập")
```

### 2. Bảng mapping trong teleport_position_mapping.gd

File `teleport_position_mapping.gd` đã có sẵn các mapping chính xác cho tất cả các cổng dịch chuyển:

```gdscript
var position_mappings: Dictionary = {
    # Lang Van Lang -> Other Maps
    "lang_van_lang_to_rung_nuong": Vector2(753, -1225),
    "lang_van_lang_to_dong_dau": Vector2(-1421, -429),
    "lang_van_lang_to_hang_an": Vector2(-2069, 484),
    "lang_van_lang_to_suoi_thieng": Vector2(-2069, 484),
    "lang_van_lang_to_doi_tre": Vector2(-2292, -538),
    
    # Rung Nuong -> Other Maps
    "rung_nuong_to_lang_van_lang": Vector2(300, -1900),
    ...
}
```

## ✅ Hướng dẫn kiểm tra

1. Mở scene `lang_van_lang.tscn`
2. Chạy scene (F6)
3. Di chuyển nhân vật đến gần cổng (thường ở các vị trí biên của map)
4. Nhấn phím Enter để dịch chuyển
5. Kiểm tra xem nhân vật có xuất hiện đúng vị trí trong map mới hay không
6. Kiểm tra UI tên bản đồ ở góc trên phải để xác nhận đã chuyển map

## 🔍 Kiểm tra debug

Nếu cần debug hệ thống teleport:

1. Mở `Test&debug/teleport_position_test.gd` 
2. Thêm scene vào scene tree của Godot và chạy
3. Xem output trong console để kiểm tra chi tiết các bước teleport

## 🧠 Lưu ý kỹ thuật

Hệ thống teleport hoạt động qua 3 bước chính:
1. Cổng gọi `SceneManager.set_next_spawn_position()` với vị trí lấy từ `TeleportPositionMapping`
2. SceneManager lưu vị trí và chuyển scene
3. Player đọc vị trí từ SceneManager và đặt vị trí của mình

Nếu có vấn đề, hãy kiểm tra:
- Log trong Output window để xem vị trí spawn có được set đúng không
- Hàm _extract_map_name_from_scene và _get_current_map_name hoạt động đúng không
- Các mapping trong TeleportPositionMapping đã chính xác chưa
