# Comprehensive Teleport System Validation
# Validates the entire teleport positioning system
extends Node

func _ready():
	print("🔍 === COMPREHENSIVE TELEPORT SYSTEM VALIDATION ===")
	await get_tree().process_frame
	await get_tree().process_frame
	
	validate_autoload_systems()
	validate_position_mappings()
	validate_scene_files()
	validate_map_controllers()
	print_final_report()

func validate_autoload_systems():
	print("\n🔧 1. VALIDATING AUTOLOAD SYSTEMS")
	
	# Check SceneManager
	if SceneManager:
		print("✅ SceneManager: Available")
		if SceneManager.has_method("set_next_spawn_position"):
			print("   ✅ set_next_spawn_position() method exists")
		else:
			print("   ❌ set_next_spawn_position() method missing")
			
		if SceneManager.has_method("get_next_spawn_position"):
			print("   ✅ get_next_spawn_position() method exists")
		else:
			print("   ❌ get_next_spawn_position() method missing")
	else:
		print("❌ SceneManager: NOT AVAILABLE")
	
	# Check TeleportPositionMapping
	if TeleportPositionMapping:
		print("✅ TeleportPositionMapping: Available")
		if TeleportPositionMapping.has_method("get_accurate_spawn_position"):
			print("   ✅ get_accurate_spawn_position() method exists")
		else:
			print("   ❌ get_accurate_spawn_position() method missing")
	else:
		print("❌ TeleportPositionMapping: NOT AVAILABLE")

func validate_position_mappings():
	print("\n📍 2. VALIDATING POSITION MAPPINGS")
	
	if not TeleportPositionMapping:
		print("❌ Cannot validate - TeleportPositionMapping not available")
		return
	
	var maps = ["lang_van_lang", "rung_nuong", "dong_dau", "hang_an", "suoi_thieng", "doi_tre"]
	var total_expected = maps.size() * (maps.size() - 1)  # All combinations except self->self
	var found_mappings = 0
	var missing_mappings = []
	
	for from_map in maps:
		for to_map in maps:
			if from_map != to_map:
				var mapping_key = from_map + "_to_" + to_map
				var spawn_pos = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
				
				if spawn_pos != Vector2.ZERO:
					found_mappings += 1
				else:
					missing_mappings.append(mapping_key)
	
	print("📊 Position Mappings: %d/%d found" % [found_mappings, total_expected])
	
	if missing_mappings.size() > 0:
		print("⚠️ Missing mappings:")
		for missing in missing_mappings:
			print("   - %s" % missing)
	else:
		print("✅ All position mappings are complete!")

func validate_scene_files():
	print("\n🎬 3. VALIDATING SCENE FILES")
	
	var scene_paths = [
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn",
		"res://maps/hang_an/scenes/hang_an.tscn",
		"res://maps/suoi_thieng/scenes/suoi_thieng.tscn",
		"res://maps/doi_tre/scenes/doi_tre.tscn"
	]
	
	var valid_scenes = 0
	
	for scene_path in scene_paths:
		if ResourceLoader.exists(scene_path):
			var resource = ResourceLoader.load(scene_path)
			if resource:
				print("✅ %s" % scene_path.get_file())
				valid_scenes += 1
			else:
				print("❌ %s (cannot load)" % scene_path.get_file())
		else:
			print("❌ %s (not found)" % scene_path.get_file())
	
	print("📊 Scene Files: %d/%d valid" % [valid_scenes, scene_paths.size()])

func validate_map_controllers():
	print("\n🎮 4. VALIDATING MAP CONTROLLERS")
	
	var controller_paths = [
		"res://maps/lang_van_lang/scripts/lang_van_lang_map_controller.gd",
		"res://maps/rung_nuong/scripts/rung_nuong_map_controller.gd",
		"res://maps/dong_dau/scripts/dong_dau_map_controller.gd",
		"res://maps/hang_an/scripts/hang_an_map_controller.gd",
		"res://maps/suoi_thieng/scripts/suoi_thieng_map_controller.gd",
		"res://maps/doi_tre/scripts/doi_tre_map_controller.gd"
	]
	
	var valid_controllers = 0
	
	for controller_path in controller_paths:
		if FileAccess.file_exists(controller_path):
			print("✅ %s" % controller_path.get_file())
			valid_controllers += 1
		else:
			print("❌ %s (not found)" % controller_path.get_file())
	
	print("📊 Map Controllers: %d/%d found" % [valid_controllers, controller_paths.size()])

func print_final_report():
	print("\n🎯 === VALIDATION REPORT ===")
	print("System Status:")
	
	if SceneManager and TeleportPositionMapping:
		print("✅ Core systems operational")
	else:
		print("❌ Core systems missing")
	
	print("\nTo test the system:")
	print("1. Enter any map scene (e.g., lang_van_lang.tscn)")
	print("2. Find a teleport gate")
	print("3. Walk into the gate area")
	print("4. Press Enter to teleport")
	print("5. Verify player spawns at correct position")
	
	print("\nExpected behavior:")
	print("When teleporting from lang_van_lang → dong_dau,")
	print("player should spawn at the location of the lang_van_lang")
	print("gate in the dong_dau map (approximately Vector2(-1421, -429))")
