[gd_scene load_steps=2 format=3 uid="uid://cvkm5yxwh44n8"]

[ext_resource type="Script" path="res://Test&debug/validation/teleport_system_tester.gd" id="1_1w2xq"]

[node name="TeleportSystemTester" type="Node2D"]
script = ExtResource("1_1w2xq")

[node name="InfoLabel" type="Label" parent="."]
offset_left = 10.0
offset_top = 10.0
offset_right = 800.0
offset_bottom = 200.0
text = "🧪 TELEPORT SYSTEM TESTER

Chức năng:
- <PERSON><PERSON><PERSON> tra toàn bộ hệ thống teleport
- Validate position mappings
- Test context manager
- Analyze teleport gates

Controls:
- Enter: Chạy comprehensive test
- Space: Debug player position hiện tại

Xem Console để theo dõi kết quả test chi tiết."
