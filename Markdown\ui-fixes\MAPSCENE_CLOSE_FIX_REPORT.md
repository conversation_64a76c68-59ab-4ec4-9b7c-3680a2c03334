# MapScene Toggle Fix - Close Issue Resolution

## 🐛 Vấn đề: "lỗi nhấn M được mở chứ không tắt được map"

### Nguyên nhân:
- MapToggleManager chỉ có logic `toggle_map_scene()` chung
- Khi ở trong MapScene, không có cách nào để detect và đóng
- Input handling conflict giữa open và close logic

### ✅ Fix đã áp dụng:

#### 1. MapScene.gd - Thêm lại input handler cho đóng
```gdscript
func _input(event):
    if event.is_action_pressed("toggle_map"):
        print("🗺️ MapScene: Detected M key press for closing")
        if SceneManager and SceneManager.is_map_scene_open():
            SceneManager.close_map_scene()
            # Ngăn input lan truyền đến MapToggleManager
            get_viewport().set_input_as_handled()
```

#### 2. MapToggleManager.gd - Chỉ handle mở, không handle đóng
```gdscript
# Nếu đang ở trong MapScene, để MapScene tự handle việc đóng
if is_in_map_scene:
    print("🗺️ Đang ở trong MapScene, MapScene sẽ handle việc đóng")
    return

# Chỉ mở MapScene nếu chưa mở
if not SceneManager.is_map_scene_open():
    SceneManager.open_map_scene()
```

## 🔄 Logic Flow mới:

### Khi ở ngoài MapScene (bất kỳ scene nào):
1. User nhấn M
2. MapToggleManager detect
3. Check: Không phải MapScene → Mở MapScene
4. set_input_as_handled()

### Khi ở trong MapScene:
1. User nhấn M  
2. MapScene._input() detect (high priority)
3. Call SceneManager.close_map_scene()
4. set_input_as_handled() → Ngăn MapToggleManager xử lý
5. Quay về scene trước đó

## 🧪 Test Instructions:

1. **Mở Godot editor**
2. **Chạy test_map_toggle.tscn**
3. **Nhấn M** → Should open MapScene
4. **Trong MapScene, nhấn M** → Should close và quay về
5. **Repeat** → Should work perfectly

### Expected Console Output:

**Lần 1 - Mở:**
```
🔍 MapToggleManager: Detected M key press
🔍 Is in MapScene: false
📂 MapToggleManager: Opening MapScene
```

**Lần 2 - Đóng:**
```
🗺️ MapScene: Detected M key press for closing
🗺️ MapScene: Calling close_map_scene()
📁 SceneManager.close_map_scene() called
```

## 📋 Separation of Concerns:

- **MapToggleManager**: Handle mở MapScene từ bất kỳ scene nào
- **MapScene**: Handle đóng MapScene khi đang ở trong nó
- **SceneManager**: Quản lý state và scene switching logic

---
**Status**: ✅ FIXED  
**Test**: Ready for verification
