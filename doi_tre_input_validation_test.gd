extends Node
# Test script để validation doi_tre input system fix

func _ready():
	print("🧪 DOI TRE INPUT VALIDATION TEST")
	print("==================================================")
	
	# Test 1: Check AutoLoad Order
	test_autoload_order()
	
	# Test 2: Check Input Actions  
	test_input_actions()
	
	# Test 3: Check EnhancedInputManager
	test_enhanced_input_manager()
	
	# Test 4: Check MapToggleManager
	test_map_toggle_manager()
	
	# Test 5: Check doi_tre scene
	test_doi_tre_scene()
	
	print("==================================================")
	print("✅ DOI TRE INPUT VALIDATION COMPLETED")

func test_autoload_order():
	print("\n🔍 TEST 1: AutoLoad Order")
	
	# Check if both managers exist
	if MapToggleManager:
		print("✅ MapToggleManager loaded")
	else:
		print("❌ MapToggleManager NOT loaded")
		
	if EnhancedInputManager:
		print("✅ EnhancedInputManager loaded")
	else:
		print("❌ EnhancedInputManager NOT loaded")
	
	# Check SceneManager
	if SceneManager:
		print("✅ SceneManager loaded")
	else:
		print("❌ SceneManager NOT loaded")

func test_input_actions():
	print("\n🔍 TEST 2: Input Actions")
	
	# Check teleport_interact (Enter)
	if InputMap.has_action("teleport_interact"):
		var events = InputMap.action_get_events("teleport_interact")
		for event in events:
			if event is InputEventKey:
				print("✅ teleport_interact: keycode=%d (Enter=4194309)" % event.physical_keycode)
	else:
		print("❌ teleport_interact action not found")
	
	# Check ui_accept (Enter)
	if InputMap.has_action("ui_accept"):
		var events = InputMap.action_get_events("ui_accept")
		for event in events:
			if event is InputEventKey:
				print("✅ ui_accept: keycode=%d (Enter=4194309)" % event.physical_keycode)
	else:
		print("❌ ui_accept action not found")
		
	# Check toggle_map (M)
	if InputMap.has_action("toggle_map"):
		var events = InputMap.action_get_events("toggle_map")
		for event in events:
			if event is InputEventKey:
				print("✅ toggle_map: keycode=%d (M=77)" % event.physical_keycode)
	else:
		print("❌ toggle_map action not found")

func test_enhanced_input_manager():
	print("\n🔍 TEST 3: EnhancedInputManager")
	
	if EnhancedInputManager:
		print("✅ EnhancedInputManager available")
		if EnhancedInputManager.has_method("_handle_map_toggle"):
			print("✅ _handle_map_toggle method exists")
		else:
			print("❌ _handle_map_toggle method missing")
	else:
		print("❌ EnhancedInputManager not available")

func test_map_toggle_manager():
	print("\n🔍 TEST 4: MapToggleManager")
	
	if MapToggleManager:
		print("✅ MapToggleManager available")
		if MapToggleManager.has_method("_is_player_in_teleport_gate"):
			print("✅ _is_player_in_teleport_gate method exists")
		else:
			print("❌ _is_player_in_teleport_gate method missing")
	else:
		print("❌ MapToggleManager not available")

func test_doi_tre_scene():
	print("\n🔍 TEST 5: Doi Tre Scene")
	
	# Check if doi_tre scene file exists
	var doi_tre_path = "res://maps/doi_tre/scenes/doi_tre.tscn"
	if ResourceLoader.exists(doi_tre_path):
		print("✅ doi_tre.tscn exists")
		
		# Try to load the scene
		var scene_resource = load(doi_tre_path)
		if scene_resource:
			print("✅ doi_tre.tscn can be loaded")
			
			# Instantiate để check structure
			var scene_instance = scene_resource.instantiate()
			if scene_instance:
				print("✅ doi_tre.tscn can be instantiated")
				
				# Check for TeleportGate
				var teleport_gate = scene_instance.get_node_or_null("TeleportGate_DoiTre")
				if teleport_gate:
					print("✅ TeleportGate_DoiTre found")
					if teleport_gate.has_method("_activate_teleport"):
						print("✅ TeleportGate has _activate_teleport method")
					else:
						print("❌ TeleportGate missing _activate_teleport method")
				else:
					print("❌ TeleportGate_DoiTre not found")
					
				# Check for Player
				var player = scene_instance.get_node_or_null("Player")
				if player:
					print("✅ Player found in scene")
				else:
					print("❌ Player not found in scene")
					
				# Clean up
				scene_instance.queue_free()
			else:
				print("❌ Cannot instantiate doi_tre scene")
		else:
			print("❌ Cannot load doi_tre scene")
	else:
		print("❌ doi_tre.tscn does not exist")

func _input(event):
	# Monitor input events for debugging
	if event.is_action_pressed("toggle_map"):
		print("🔍 TEST INPUT: M key (toggle_map) detected")
	elif event.is_action_pressed("ui_accept"):
		print("🔍 TEST INPUT: Enter key (ui_accept) detected")
	elif event.is_action_pressed("teleport_interact"):
		print("🔍 TEST INPUT: Enter key (teleport_interact) detected")
