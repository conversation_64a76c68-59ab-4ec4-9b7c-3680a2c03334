# Script để cập nhật kích thước tất cả TeleportGate về chuẩn EnemyGate (50x100)

$teleportGateFiles = @(
    "maps\lang_van_lang\scenes\TeleportGate_LangVanLang_RungNuong.tscn",
    "maps\lang_van_lang\scenes\TeleportGate_LangVanLang_DongDau.tscn", 
    "maps\lang_van_lang\scenes\TeleportGate_LangVanLang_DoiTre.tscn",
    "maps\lang_van_lang\scenes\TeleportGate_LangVanLang_SuoiThieng.tscn",
    "maps\lang_van_lang\scenes\TeleportGate.tscn",
    "maps\rung_nuong\scenes\TeleportGate_RungNuong.tscn",
    "maps\rung_nuong\scenes\TeleportGate_RungNuong_HangAn.tscn",
    "maps\rung_nuong\scenes\TeleportGate_RungNuong_LangVanLang.tscn",
    "maps\rung_nuong\scenes\TeleportGate_RungNuong_SuoiThieng.tscn",
    "maps\suoi_thieng\scenes\TeleportGate_SuoiThieng.tscn",
    "maps\hang_an\scenes\TeleportGate_HangAn.tscn",
    "maps\hang_an\scenes\TeleportGate_HangAn_LangVanLang.tscn",
    "maps\dong_dau\scenes\TeleportGate_DongDau.tscn",
    "maps\dong_dau\scenes\TeleportGate_DongDau_DoiTre.tscn",
    "maps\dong_dau\scenes\TeleportGate_DongDau_LangVanLang.tscn",
    "maps\doi_tre\scenes\TeleportGate_DoiTre.tscn",
    "maps\scenes\TeleportGate.tscn"
)

$replacements = @(
    @{ "from" = "size = Vector2(100, 150)"; "to" = "size = Vector2(50, 100)" },
    @{ "from" = "size = Vector2(120, 180)"; "to" = "size = Vector2(50, 100)" },
    @{ "from" = "gate_size = Vector2(100, 150)"; "to" = "gate_size = Vector2(50, 100)" },
    @{ "from" = "gate_size = Vector2(120, 180)"; "to" = "gate_size = Vector2(50, 100)" },
    @{ "from" = "offset_left = -50.0"; "to" = "offset_left = -25.0" },
    @{ "from" = "offset_right = 50.0"; "to" = "offset_right = 25.0" },
    @{ "from" = "offset_top = -75.0"; "to" = "offset_top = -50.0" },
    @{ "from" = "offset_bottom = 75.0"; "to" = "offset_bottom = 50.0" },
    @{ "from" = "offset_left = -60.0"; "to" = "offset_left = -25.0" },
    @{ "from" = "offset_right = 60.0"; "to" = "offset_right = 25.0" },
    @{ "from" = "offset_top = -90.0"; "to" = "offset_top = -50.0" },
    @{ "from" = "offset_bottom = 90.0"; "to" = "offset_bottom = 50.0" }
)

$updatedFiles = @()
$errorFiles = @()

foreach ($file in $teleportGateFiles) {
    $fullPath = "d:\Du_An_Game_Nam_Quoc_Son_Ha\$file"
    
    if (Test-Path $fullPath) {
        try {
            $content = Get-Content $fullPath -Raw
            $originalContent = $content
            
            foreach ($replacement in $replacements) {
                $content = $content.Replace($replacement.from, $replacement.to)
            }
            
            if ($content -ne $originalContent) {
                Set-Content -Path $fullPath -Value $content -NoNewline
                $updatedFiles += $file
                Write-Host "✅ Updated: $file" -ForegroundColor Green
            } else {
                Write-Host "ℹ️  No changes needed: $file" -ForegroundColor Yellow
            }
        } catch {
            $errorFiles += $file
            Write-Host "❌ Error updating: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Red
    }
}
}

Write-Host "`n📊 SUMMARY:"
Write-Host "Updated files: $($updatedFiles.Count)" -ForegroundColor Green
Write-Host "Error files: $($errorFiles.Count)" -ForegroundColor Red
Write-Host "Total processed: $($teleportGateFiles.Count)"

if ($updatedFiles.Count -gt 0) {
    Write-Host "`n✅ Successfully updated files:"
    foreach ($file in $updatedFiles) {
        Write-Host "  - $file"
    }
}

if ($errorFiles.Count -gt 0) {
    Write-Host "`n❌ Files with errors:"
    foreach ($file in $errorFiles) {
        Write-Host "  - $file"
    }
}
