# quick_teleport_test.gd - Quick test để verify TeleportContextManager
extends Node

func _ready():
	print("🧪 Quick Teleport Context Test")
	await get_tree().process_frame  # Đ<PERSON>i autoloads load xong
	test_context_manager()

func test_context_manager():
	print("\n=== Testing TeleportContextManager ===")
	
	# Test 1: Ki<PERSON><PERSON> tra autoload
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		print("❌ TeleportContextManager not found - cần restart Godot")
		return
	
	print("✅ TeleportContextManager found")
	
	# Test 2: Tạo context
	context_manager.set_teleport_context(
		"lang_van_lang_to_dong_dau",
		"lang_van_lang", 
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	)
	
	# Test 3: Validate context
	if context_manager.validate_context():
		print("✅ Context validation passed")
		print("📋 Context summary: %s" % context_manager.get_context_summary())
	else:
		print("❌ Context validation failed")
		return
	
	# Test 4: <PERSON><PERSON><PERSON> tra position
	if context_manager.has_valid_position():
		print("✅ Valid position found")
		var pos = context_manager.get_spawn_position_for_current_context()
		print("📍 Retrieved position: %s" % pos)
	else:
		print("❌ No valid position")
	
	# Test 5: Context đã được clear chưa
	if context_manager.has_active_teleport_context():
		print("⚠️ Context still active after retrieval")
	else:
		print("✅ Context properly cleared")
	
	print("\n🎯 Test completed!")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		test_context_manager()
