extends Node
class_name ButtonManager

# 🎮 Quản lý tất cả button trong game
# Script này quản lý behavior chung cho tất cả các button

# 📡 Signals
signal button_pressed(button_name: String, target_scene: String)
signal button_hovered(button_name: String, title: String, description: String)
signal button_unhovered(button_name: String)

# 🎯 Constants
const BUTTON_SCALE_NORMAL = Vector2(1.0, 1.0)
const BUTTON_SCALE_HOVER = Vector2(1.05, 1.05)
const BUTTON_SCALE_PRESSED = Vector2(0.95, 0.95)

const COLOR_NORMAL = Color(1, 1, 1, 1)
const COLOR_HOVER = Color(1.1, 1.1, 1.1, 1)
const COLOR_PRESSED = Color(0.9, 0.9, 0.9, 1)
const COLOR_LOCKED = Color(0.6, 0.6, 0.6, 1)

# 📊 Button registry
var registered_buttons: Dictionary = {}

# 🛠️ Core Functions
func register_button(button: BaseButton, config: Dictionary) -> void:
	"""Đăng ký một button với ButtonManager"""
	if not button:
		push_error("ButtonManager: Không thể đăng ký button null")
		return
	
	var button_id = config.get("id", button.name)
	
	# Lưu thông tin button
	registered_buttons[button_id] = {
		"button": button,
		"config": config,
		"original_scale": button.scale,
		"original_modulate": button.modulate,
		"is_locked": config.get("is_locked", false),
		"target_scene": config.get("target_scene", ""),
		"title": config.get("title", ""),
		"description": config.get("description", "")
	}
	
	# Kết nối signals
	_connect_button_signals(button, button_id)
	
	print("ButtonManager: Đã đăng ký button '%s'" % button_id)

func unregister_button(button_id: String) -> void:
	"""Hủy đăng ký button"""
	if registered_buttons.has(button_id):
		var button_data = registered_buttons[button_id]
		var button = button_data.button
		
		# Disconnect signals
		if button and is_instance_valid(button):
			_disconnect_button_signals(button)
		
		registered_buttons.erase(button_id)
		print("ButtonManager: Đã hủy đăng ký button '%s'" % button_id)

func _connect_button_signals(button: BaseButton, button_id: String) -> void:
	"""Kết nối signals cho button"""
	if not button:
		return
	
	# Disconnect existing connections first
	_disconnect_button_signals(button)
	
	# Connect new signals
	button.mouse_entered.connect(_on_button_mouse_entered.bind(button_id))
	button.mouse_exited.connect(_on_button_mouse_exited.bind(button_id))
	button.pressed.connect(_on_button_pressed.bind(button_id))
	
	# For TextureButton, also connect button_down/up for visual feedback
	if button is TextureButton:
		button.button_down.connect(_on_button_down.bind(button_id))
		button.button_up.connect(_on_button_up.bind(button_id))

func _disconnect_button_signals(button: BaseButton) -> void:
	"""Ngắt kết nối signals của button"""
	if not button or not is_instance_valid(button):
		return
	
	# Safely disconnect if connected
	for signal_name in ["mouse_entered", "mouse_exited", "pressed", "button_down", "button_up"]:
		if button.has_signal(signal_name):
			var connections = button.get_signal_connection_list(signal_name)
			for connection in connections:
				if connection.callable.get_object() == self:
					button.disconnect(signal_name, connection.callable)

# 🎭 Event Handlers
func _on_button_mouse_entered(button_id: String) -> void:
	"""Xử lý khi chuột hover vào button"""
	if not registered_buttons.has(button_id):
		return
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	
	if not button or not is_instance_valid(button):
		return
	
	# Visual effects
	if not button_data.is_locked:
		var tween = create_tween()
		tween.tween_property(button, "scale", BUTTON_SCALE_HOVER, 0.1)
		tween.parallel().tween_property(button, "modulate", COLOR_HOVER, 0.1)
	
	# Emit hover signal
	var title = button_data.config.get("title", "")
	var description = button_data.config.get("description", "")
	
	if button_data.is_locked:
		description = "Khu vực này hiện đang bị khóa. Hãy hoàn thành nhiệm vụ trước để mở khóa."
	
	button_hovered.emit(button_id, title, description)
	print("ButtonManager: Button '%s' hovered" % button_id)

func _on_button_mouse_exited(button_id: String) -> void:
	"""Xử lý khi chuột rời khỏi button"""
	if not registered_buttons.has(button_id):
		return
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	
	if not button or not is_instance_valid(button):
		return
	
	# Visual effects
	# Áp dụng style tùy theo trạng thái
	var target_scale = button_data.original_scale if not button_data.is_locked else button_data.original_scale
	var target_modulate = button_data.original_modulate if not button_data.is_locked else COLOR_LOCKED
	
	var tween = create_tween()
	tween.tween_property(button, "scale", target_scale, 0.1)
	tween.parallel().tween_property(button, "modulate", target_modulate, 0.1)
	
	# Emit unhover signal
	button_unhovered.emit(button_id)

func _on_button_down(button_id: String) -> void:
	"""Xử lý khi button được nhấn xuống"""
	if not registered_buttons.has(button_id):
		return
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	
	if not button or not is_instance_valid(button) or button_data.is_locked:
		return
	
	# Press effect
	var tween = create_tween()
	tween.tween_property(button, "scale", BUTTON_SCALE_PRESSED, 0.05)
	tween.parallel().tween_property(button, "modulate", COLOR_PRESSED, 0.05)

func _on_button_up(button_id: String) -> void:
	"""Xử lý khi button được thả ra"""
	if not registered_buttons.has(button_id):
		return
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	
	if not button or not is_instance_valid(button):
		return
	
	# Return to hover state if still being hovered, otherwise return to normal
	var is_hovered = false
	if button.has_method("is_hovered"):
		is_hovered = button.is_hovered()
	
	var target_scale = BUTTON_SCALE_HOVER if is_hovered else button_data.original_scale
	var target_modulate = COLOR_HOVER if is_hovered else button_data.original_modulate
	
	var tween = create_tween()
	tween.tween_property(button, "scale", target_scale, 0.05)
	tween.parallel().tween_property(button, "modulate", target_modulate, 0.05)

func _on_button_pressed(button_id: String) -> void:
	"""Xử lý khi button được click"""
	if not registered_buttons.has(button_id):
		print("ButtonManager: Button '%s' not found in registry" % button_id)
		return
	
	var button_data = registered_buttons[button_id]
	
	if button_data.is_locked:
		print("ButtonManager: Button '%s' is locked!" % button_id)
		_show_locked_message(button_id)
		return
	
	var target_scene = button_data.config.get("target_scene", "")
	
	if target_scene.is_empty():
		push_error("ButtonManager: Button '%s' không có target_scene" % button_id)
		return
	
	if not FileAccess.file_exists(target_scene):
		push_error("ButtonManager: Scene không tồn tại: %s" % target_scene)
		return
	
	print("ButtonManager: Button '%s' pressed, changing to %s" % [button_id, target_scene])
	
	# Emit signal
	button_pressed.emit(button_id, target_scene)
	
	# Change scene with delay for visual feedback
	await get_tree().create_timer(0.1).timeout
	SceneManager.goto_scene(target_scene)

# 🔒 Lock/Unlock Functions
func lock_button(button_id: String) -> void:
	"""Khóa một button"""
	if not registered_buttons.has(button_id):
		print("ButtonManager: Button '%s' not found for locking" % button_id)
		return
	
	registered_buttons[button_id].is_locked = true
	
	var button = registered_buttons[button_id].button
	if button and is_instance_valid(button):
		button.modulate = COLOR_LOCKED
	
	print("ButtonManager: Đã khóa button '%s'" % button_id)

func unlock_button(button_id: String) -> void:
	"""Mở khóa một button"""
	if not registered_buttons.has(button_id):
		print("ButtonManager: Button '%s' not found for unlocking" % button_id)
		return
	
	registered_buttons[button_id].is_locked = false
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	if button and is_instance_valid(button):
		button.modulate = button_data.original_modulate
	
	print("ButtonManager: Đã mở khóa button '%s'" % button_id)

func is_button_locked(button_id: String) -> bool:
	"""Kiểm tra xem button có bị khóa không"""
	if not registered_buttons.has(button_id):
		return false
	
	return registered_buttons[button_id].is_locked

# 💬 UI Functions
func _show_locked_message(button_id: String) -> void:
	"""Hiển thị thông báo khi button bị khóa"""
	if not registered_buttons.has(button_id):
		return
	
	var button_data = registered_buttons[button_id]
	var button = button_data.button
	if not button or not is_instance_valid(button):
		return
	
	# Create a simple notification label
	var lock_notification = Label.new()
	lock_notification.text = "Khu vực này đang bị khóa!"
	lock_notification.add_theme_color_override("font_color", Color.RED)
	lock_notification.position = button.global_position + Vector2(0, -50)
	lock_notification.z_index = 100
	
	# Add to scene tree
	get_tree().current_scene.add_child(notification)
	
	# Auto-remove after 2 seconds
	await get_tree().create_timer(2.0).timeout
	
	var fade_tween = create_tween()
	fade_tween.tween_property(notification, "modulate:a", 0.0, 0.5)
	await fade_tween.finished
	notification.queue_free()

# 📊 Utility Functions
func get_button_count() -> int:
	"""Lấy số lượng button đã đăng ký"""
	return registered_buttons.size()

func get_registered_button_ids() -> Array:
	"""Lấy danh sách ID của tất cả button đã đăng ký"""
	return registered_buttons.keys()

func get_button_info(button_id: String) -> Dictionary:
	"""Lấy thông tin của một button"""
	if not registered_buttons.has(button_id):
		return {}
	
	var button_data = registered_buttons[button_id]
	return {
		"id": button_id,
		"is_locked": button_data.is_locked,
		"target_scene": button_data.config.get("target_scene", ""),
		"title": button_data.config.get("title", ""),
		"description": button_data.config.get("description", ""),
		"button_valid": button_data.button != null and is_instance_valid(button_data.button)
	}

# 🧹 Cleanup
func _exit_tree() -> void:
	"""Cleanup khi script bị xóa"""
	for button_id in registered_buttons.keys():
		unregister_button(button_id)
	
	print("ButtonManager: Cleaned up")
