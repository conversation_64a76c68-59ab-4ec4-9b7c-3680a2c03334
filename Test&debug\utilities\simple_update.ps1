# Script đơn gi<PERSON>n để cập nhật kích thước TeleportGate
Write-Host "Bắt đầu cập nhật kích thước TeleportGate..."

$files = Get-ChildItem -Path "maps" -Recurse -Name "TeleportGate*.tscn"
$count = 0

foreach ($file in $files) {
    $fullPath = "d:\Du_An_Game_Nam_Quoc_Son_Ha\maps\$($file.Replace('maps\', ''))"
    
    if (Test-Path $fullPath) {
        $content = Get-Content $fullPath -Raw
        
        # Thay thế kích thước collision shape
        $content = $content.Replace("size = Vector2(100, 150)", "size = Vector2(50, 100)")
        $content = $content.Replace("size = Vector2(120, 180)", "size = Vector2(50, 100)")
        
        # Thay thế gate_size
        $content = $content.Replace("gate_size = Vector2(100, 150)", "gate_size = Vector2(50, 100)")
        $content = $content.Replace("gate_size = Vector2(120, 180)", "gate_size = Vector2(50, 100)")
        
        # Thay thế visual offset
        $content = $content.Replace("offset_left = -50.0", "offset_left = -25.0")
        $content = $content.Replace("offset_right = 50.0", "offset_right = 25.0")
        $content = $content.Replace("offset_top = -75.0", "offset_top = -50.0")
        $content = $content.Replace("offset_bottom = 75.0", "offset_bottom = 50.0")
        $content = $content.Replace("offset_left = -60.0", "offset_left = -25.0")
        $content = $content.Replace("offset_right = 60.0", "offset_right = 25.0")
        $content = $content.Replace("offset_top = -90.0", "offset_top = -50.0")
        $content = $content.Replace("offset_bottom = 90.0", "offset_bottom = 50.0")
        
        Set-Content -Path $fullPath -Value $content -NoNewline
        Write-Host "✅ Updated: $file"
        $count++
    }
}

Write-Host "Hoàn thành! Đã cập nhật $count files."
