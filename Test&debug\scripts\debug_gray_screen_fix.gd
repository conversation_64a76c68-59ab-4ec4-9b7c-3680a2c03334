# Debug Gray Screen Fix - Phân tích lỗi teleport dong_dau → doi_tre
extends Node2D

func _ready():
	print("� === DEBUGGING GRAY SCREEN ISSUE ===")
	
	# Wait for scene to be fully loaded
	await get_tree().process_frame
	await get_tree().process_frame
	
	debug_gray_screen_issue()
	test_current_scene()
	test_player()
	test_camera()
	test_tilemap()

func debug_gray_screen_issue():
	print("\n📍 1. Checking TeleportPositionMapping...")
	if TeleportPositionMapping:
		print("✅ TeleportPositionMapping available")
		var dong_dau_to_doi_tre = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
		print("🎯 dong_dau → doi_tre position: %s" % dong_dau_to_doi_tre)
		
		var default_doi_tre = TeleportPositionMapping.get_default_spawn_position("doi_tre")
		print("🔄 Default doi_tre position: %s" % default_doi_tre)
	else:
		print("❌ TeleportPositionMapping not found")
	
	print("\n🚪 2. Checking TeleportGate configurations...")
	print("From dong_dau scene:")
	print("  - TeleportGate_DongDau_DoiTre target_position: Vector2(500, -200)")
	print("  - Issue: Position (500, -200) có thể nằm ngoài vùng tilemap")
	
	print("\n👤 3. Player position analysis:")
	print("  - Player default in doi_tre: Vector2(-2292, -538)")
	print("  - TeleportGate spawn target: Vector2(500, -200)")
	print("  - PROBLEM: Spawn position cách xa vùng an toàn")

func test_current_scene():
	print("\n🎬 Current Scene Test:")
	var current_scene = get_tree().current_scene
	if current_scene:
		print("✅ Current scene: %s" % current_scene.name)
		print("   Path: %s" % current_scene.scene_file_path)
		print("   Children count: %d" % current_scene.get_child_count())
		
		# List all children
		for child in current_scene.get_children():
			print("   - %s (%s)" % [child.name, child.get_class()])
	else:
		print("❌ No current scene")

func test_player():
	print("\n👤 Player Test:")
	var player = get_tree().get_first_node_in_group("player")
	if not player:
		player = get_tree().current_scene.get_node_or_null("Player")
	
	if player:
		print("✅ Player found: %s" % player.name)
		print("   Position: %s" % player.global_position)
		print("   Visible: %s" % player.visible)
		print("   Modulate: %s" % player.modulate)
	else:
		print("❌ Player not found")

func test_camera():
	print("\n📹 Camera Test:")
	var camera = get_viewport().get_camera_2d()
	if camera:
		print("✅ Camera found: %s" % camera.name)
		print("   Global position: %s" % camera.global_position)
		print("   Zoom: %s" % camera.zoom)
		print("   Enabled: %s" % camera.enabled)
		print("   Current: %s" % camera.is_current())
	else:
		print("❌ No active camera")

func test_tilemap():
	print("\n🗺️ TileMap Test:")
	var tilemaps = get_tree().get_nodes_in_group("tilemap")
	if tilemaps.is_empty():
		# Try to find TileMap nodes directly
		var all_tilemaps = []
		_find_tilemaps_recursive(get_tree().current_scene, all_tilemaps)
		
		print("   Found %d TileMap nodes:" % all_tilemaps.size())
		for tilemap in all_tilemaps:
			print("   - %s: visible=%s, modulate=%s" % [tilemap.name, tilemap.visible, tilemap.modulate])
	else:
		print("✅ Found %d tilemap nodes in group" % tilemaps.size())

func _find_tilemaps_recursive(node: Node, result: Array):
	if node is TileMap or node is TileMapLayer:
		result.append(node)
	
	for child in node.get_children():
		_find_tilemaps_recursive(child, result)

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Refresh test results:")
		test_current_scene()
		test_player()
		test_camera()
		test_tilemap()
