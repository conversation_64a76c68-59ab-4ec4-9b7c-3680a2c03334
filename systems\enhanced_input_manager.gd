# Enhanced Input Manager - Xử lý input toàn cục cho game
extends Node

# Autoload script để xử lý các phím input chính

func _ready():
	print("⌨️ Enhanced Input Manager initialized")
	# Đặt process mode để có thể nhận input ngay cả khi game pause
	process_mode = Node.PROCESS_MODE_ALWAYS

func _input(event):
	# Xử lý các input không liên quan đến teleport
	_handle_map_toggle(event)
	_handle_debug_inputs(event)

func _handle_map_toggle(event):
	"""Xử lý phím mở MapScene - CHỈ XỬ LÝ PHÍM M"""
	# CHỖ PHÍM M để toggle map, để phím Enter cho teleport system
	var should_toggle_map = false
	
	if event.is_action_pressed("toggle_map"):  # Phím M
		should_toggle_map = true
		print("🔍 Enhanced Input Manager: M key pressed for map toggle")
	
	# KHÔNG xử lý phím Enter ở đây - để MapToggleManager và TeleportGate handle
	
	if should_toggle_map:
		_handle_map_toggle_logic()
		# Prevent further processing ONLY for M key
		var viewport = get_viewport()
		if viewport:
			viewport.set_input_as_handled()

func _is_near_teleport_gate() -> bool:
	"""Kiểm tra xem player có đang gần teleport gate không"""
	var player = get_tree().get_first_node_in_group("player")
	if not player:
		return false
	
	# Tìm tất cả teleport gates
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	for gate in gates:
		if gate is TeleportGate and is_instance_valid(gate):
			# Kiểm tra xem player có đang trong gate không
			if gate.get_player_inside_state():
				return true
	
	return false

func _handle_map_toggle_logic():
	"""Logic toggle MapScene (sao chép từ MapToggleManager)"""
	print("🗺️ Enhanced Input Manager: Toggle MapScene")
	
	# Debug SceneManager state
	if SceneManager:
		print("🔍 Current SceneManager state: is_map_scene_open = %s" % SceneManager.is_map_scene_open())
	else:
		print("⚠️ SceneManager not available")
		return
	
	# Kiểm tra current scene
	var current_scene = get_tree().current_scene
	if not current_scene or not is_instance_valid(current_scene):
		print("⚠️ Current scene không tồn tại hoặc không hợp lệ")
		return
	
	print("🔍 Current scene: %s" % current_scene.name)
	
	# Kiểm tra scene path
	var current_scene_path = ""
	if current_scene.scene_file_path:
		current_scene_path = current_scene.scene_file_path
	print("🔍 Current scene path: '%s'" % current_scene_path)
	
	# Forbidden scenes
	var forbidden_scenes = [
		"res://Home/scenes/Startmenu.tscn",
		"res://ui/scenes/loading_screen.tscn"
	]
	
	# Không cho phép toggle map trong các scene cấm
	if current_scene_path != "":
		for forbidden_path in forbidden_scenes:
			if current_scene_path == forbidden_path:
				print("🚫 Không thể mở MapScene từ scene: %s" % current_scene_path)
				return
	
	# Check if trong MapScene
	var is_in_map_scene = current_scene_path == "res://maps/New_loor/scenes/MapScene.tscn" or current_scene.name == "MapScene"
	print("🔍 Is in MapScene: %s" % is_in_map_scene)
	
	# Nếu đang ở trong MapScene, để MapScene tự handle việc đóng
	if is_in_map_scene:
		print("🗺️ Đang ở trong MapScene, MapScene sẽ handle việc đóng")
		return
	
	# Gọi SceneManager để toggle MapScene
	if SceneManager:
		if not SceneManager.is_map_scene_open():
			print("📂 Enhanced Input Manager: Opening MapScene")
			SceneManager.open_map_scene()
		else:
			print("🔍 MapScene đã mở, không cần action")
	else:
		print("❌ SceneManager không tồn tại")

func _handle_debug_inputs(event):
	"""Xử lý các phím debug"""
	if event.is_action_pressed("ui_cancel"):  # ESC key
		print("🔍 Debug: ESC key pressed")
		# Có thể thêm logic debug ở đây
