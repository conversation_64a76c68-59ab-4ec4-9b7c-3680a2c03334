# Gray Screen Fix Test Script
extends Node2D

func _ready():
	print("🧪 === GRAY SCREEN FIX VERIFICATION ===")
	await get_tree().process_frame
	
	test_teleport_positions()
	test_scene_integrity()
	verify_fixes()

func test_teleport_positions():
	print("\n📍 Testing Teleport Position Mappings:")
	
	if TeleportPositionMapping:
		var dong_dau_to_doi_tre = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
		print("✅ dong_dau → doi_tre: %s" % dong_dau_to_doi_tre)
		
		var doi_tre_to_dong_dau = TeleportPositionMapping.get_accurate_spawn_position("doi_tre", "dong_dau")
		print("✅ doi_tre → dong_dau: %s" % doi_tre_to_dong_dau)
		
		# Verify positions are in safe range
		if dong_dau_to_doi_tre.x < 0 and dong_dau_to_doi_tre.y < 0:
			print("✅ doi_tre spawn position is in safe tilemap range")
		else:
			print("⚠️ doi_tre spawn position may be outside tilemap")
			
		if doi_tre_to_dong_dau.x < 0 and doi_tre_to_dong_dau.y < 0:
			print("✅ dong_dau spawn position is in safe tilemap range")
		else:
			print("⚠️ dong_dau spawn position may be outside tilemap")
	else:
		print("❌ TeleportPositionMapping not available")

func test_scene_integrity():
	print("\n🎬 Testing Scene Integrity:")
	
	# Test doi_tre scene
	if ResourceLoader.exists("res://maps/doi_tre/scenes/doi_tre.tscn"):
		print("✅ doi_tre.tscn exists")
		
		# Try to load scene to verify integrity
		var scene_resource = load("res://maps/doi_tre/scenes/doi_tre.tscn")
		if scene_resource:
			print("✅ doi_tre.tscn loads successfully")
			
			# Test instantiate
			var scene_instance = scene_resource.instantiate()
			if scene_instance:
				print("✅ doi_tre.tscn can be instantiated")
				
				# Check for required nodes
				var player = scene_instance.get_node_or_null("Player")
				var controller = scene_instance.get_node_or_null("DoiTreMapController")
				var teleport_gate = scene_instance.get_node_or_null("TeleportGate_DoiTre")
				
				print("   - Player node: %s" % ("✅ Found" if player else "❌ Missing"))
				print("   - Map Controller: %s" % ("✅ Found" if controller else "❌ Missing"))
				print("   - Teleport Gate: %s" % ("✅ Found" if teleport_gate else "❌ Missing"))
				
				scene_instance.queue_free()
			else:
				print("❌ Failed to instantiate doi_tre.tscn")
		else:
			print("❌ Failed to load doi_tre.tscn")
	else:
		print("❌ doi_tre.tscn does not exist")

func verify_fixes():
	print("\n🔧 Verifying Applied Fixes:")
	
	print("1. ✅ Updated TeleportPositionMapping:")
	print("   - dong_dau_to_doi_tre: Vector2(-2292, -538)")
	print("   - doi_tre_to_dong_dau: Vector2(-1421, -429)")
	
	print("2. ✅ Updated TeleportGate target_positions:")
	print("   - TeleportGate_DongDau_DoiTre: Vector2(-2292, -538)")
	print("   - TeleportGate_DoiTre: Vector2(-1421, -429)")
	
	print("3. ✅ Enhanced teleport_gate.gd debug logging")
	
	print("\n🎯 EXPECTED RESULTS:")
	print("   - Player should spawn at Vector2(-2292, -538) in doi_tre")
	print("   - This position is near the default Player position in scene")
	print("   - Player should be visible with proper tilemap background")
	print("   - Camera should follow player correctly")
	
	print("\n🧪 === VERIFICATION COMPLETED ===")
	
	# Auto cleanup after test
	await get_tree().create_timer(2.0).timeout
	queue_free()
