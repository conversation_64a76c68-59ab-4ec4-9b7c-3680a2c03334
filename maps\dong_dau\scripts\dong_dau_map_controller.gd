# DongDauMapController.gd - Controller cho map Đồng Đậu
extends Node2D
class_name DongDauMapController

# ----- Signals -----
signal map_loaded
signal teleport_gate_activated(gate_id: String)

# ----- Node References -----
@onready var player: Player = null
@onready var teleport_gates: Array[TeleportGate] = []

# ----- Map Settings -----
var map_id: String = "dong_dau"
var map_name: String = "Đồng Đậu"

func _ready() -> void:
	# Add to map controllers group for debugging
	add_to_group("map_controllers")
	
	print("🏞️ Dong Dau Map Controller initialized")
	call_deferred("_setup_map")

func _setup_map() -> void:
	# Tìm player trong scene
	_find_player()

	# Tìm và setup các cổng dịch chuyển
	_setup_teleport_gates()

	# Hiển thị tên bản đồ
	_show_map_name_ui()

	# Emit signal map đã load xong
	map_loaded.emit()
	print("✅ Dong Dau map setup completed")
	
	# 🎯 CRITICAL FIX: Delay teleport position fixing to ensure player is found
	call_deferred("_check_and_setup_teleport_spawn")

func _show_map_name_ui() -> void:
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name(map_name)
		GlobalMapNameUI.show_map_name()
		print("🗺️ Updated global map name UI: %s" % map_name)
	else:
		print("⚠️ GlobalMapNameUI not available")

func _find_player() -> void:
	# Tìm player node
	player = get_tree().get_first_node_in_group("player")
	if not player:
		# Thử tìm theo đường dẫn
		player = get_node_or_null("Player")
	
	if player:
		print("👤 Player found in Dong Dau map: %s" % player.name)
		print("Player position: ", player.global_position)
		
		# 🎯 CRITICAL FIX: Don't call _auto_fix_teleport_position here
		# It will be called later by _check_and_setup_teleport_spawn()
	else:
		print("⚠️ WARNING: Player not found in Dong Dau map")

func _auto_fix_teleport_position() -> void:
	"""Tự động sửa vị trí player nếu đến từ teleport"""
	if not player:
		print("⚠️ No player found for position fixing")
		return
	
	print("🎯 Auto-fixing teleport position...")
	
	var target_pos = Vector2.ZERO
	var positioning_method = ""
	
	# Method 1: Sử dụng SceneManager (simpler and more reliable)
	if SceneManager and SceneManager.has_method("has_next_spawn_position") and SceneManager.has_next_spawn_position():
		target_pos = SceneManager.get_and_clear_spawn_position()
		positioning_method = "SceneManager"
		print("🎯 Dong Dau: Using SceneManager spawn position: %s" % target_pos)
	
	# Method 2: Fallback to TeleportContextManager
	elif get_node_or_null("/root/TeleportContextManager"):
		var context_manager = get_node_or_null("/root/TeleportContextManager")
		if context_manager.has_method("has_active_teleport_context") and context_manager.has_active_teleport_context():
			target_pos = context_manager.get_spawn_position_for_current_context()
			positioning_method = "TeleportContextManager"
			print("🎯 Using TeleportContextManager spawn position: %s" % target_pos)
	
	# Apply position if valid
	if target_pos != Vector2.ZERO:
		print("🎯 Auto-fixing player position from %s to %s via %s" % [player.global_position, target_pos, positioning_method])
		player.global_position = target_pos
		print("✅ Player repositioned successfully to: %s" % player.global_position)
		
		# Note: Spawn position already cleared by get_and_clear_spawn_position()
	else:
		print("📍 No teleport spawn position available, using default dong_dau position")
		_set_default_spawn_position()
	
	# Ensure player is in a safe position
	_validate_player_position()

func _set_default_spawn_position() -> void:
	"""Set player to default safe position for dong_dau map"""
	var safe_position = Vector2(-1421, -429)  # Default dong_dau position
	player.global_position = safe_position
	print("🏠 Player set to default dong_dau position: %s" % safe_position)

func _validate_player_position() -> void:
	"""Validate current player position is within map bounds"""
	if not player:
		return
	
	var current_pos = player.global_position
	print("📍 Current player position: %s" % current_pos)
	
	# If player is at origin or very far from expected areas, fix it
	if current_pos == Vector2.ZERO or current_pos.distance_to(Vector2(-1421, -429)) > 5000:
		print("⚠️ Player position seems invalid, fixing...")
		_set_default_spawn_position()

func _setup_teleport_gates() -> void:
	# Tìm tất cả cổng dịch chuyển trong scene
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	for gate in gates:
		if gate is TeleportGate:
			teleport_gates.append(gate)
			_connect_gate_signals(gate)
			print("Connected teleport gate: ", gate.gate_id)

func _connect_gate_signals(gate: TeleportGate) -> void:
	if not gate:
		return
	
	# Kết nối signals của cổng
	if not gate.player_entered_gate.is_connected(_on_player_entered_gate):
		gate.player_entered_gate.connect(_on_player_entered_gate)
	
	if not gate.player_exited_gate.is_connected(_on_player_exited_gate):
		gate.player_exited_gate.connect(_on_player_exited_gate)

func _on_player_entered_gate(gate: TeleportGate) -> void:
	print("Player entered teleport gate: ", gate.gate_id)
	emit_signal("teleport_gate_activated", gate.gate_id)

func _on_player_exited_gate(gate: TeleportGate) -> void:
	print("Player exited teleport gate: ", gate.gate_id)

# Hàm để thêm cổng dịch chuyển mới
func add_teleport_gate(gate: TeleportGate) -> void:
	if gate and not teleport_gates.has(gate):
		teleport_gates.append(gate)
		_connect_gate_signals(gate)
		
		# Thêm vào group để dễ tìm kiếm
		if not gate.is_in_group("teleport_gates"):
			gate.add_to_group("teleport_gates")
		
		print("Added new teleport gate: ", gate.gate_id)

# Hàm để lấy thông tin các cổng dịch chuyển
func get_teleport_gates() -> Array[TeleportGate]:
	return teleport_gates

func get_teleport_gate_by_id(gate_id: String) -> TeleportGate:
	for gate in teleport_gates:
		if gate.gate_id == gate_id:
			return gate
	return null

func _check_and_setup_teleport_spawn() -> void:
	"""Kiểm tra và setup vị trí spawn cho player nếu đến từ teleport"""
	if not player:
		print("⚠️ No player found for teleport spawn setup")
		return
	
	print("🎯 Checking teleport spawn position...")
	
	# Check if SceneManager has spawn position
	if SceneManager and SceneManager.has_method("has_next_spawn_position"):
		if SceneManager.has_next_spawn_position():
			print("📍 SceneManager has spawn position, calling _auto_fix_teleport_position")
			_auto_fix_teleport_position()
		else:
			print("📍 No teleport spawn position available")
	else:
		print("📍 No teleport spawn system available")