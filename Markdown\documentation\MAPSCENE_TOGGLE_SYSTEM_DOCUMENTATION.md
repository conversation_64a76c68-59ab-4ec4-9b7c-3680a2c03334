# MapScene Toggle System - <PERSON><PERSON> thống M Key Toggle

## 📖 Tổng quan
Hệ thống này cho phép người chơi sử dụng phím **M** để mở/đóng MapScene từ bất kỳ scene nào trong game theo kiểu toggle. <PERSON>hi đang ở trong MapScene, nhấn M lần nữa sẽ quay về scene trước đó.

## 🎮 Cách sử dụng

### Từ bất kỳ map nào:
1. **Nhấn phím M** → Mở MapScene
2. **Trong MapScene, nhấn M lại** → Quay về map cũ

### Điều khiển cơ bản:
- **M**: Toggle MapScene on/off
- **Enter**: Teleport (khi ở gần cổng dịch chuyển)
- **WASD**: Di chuyển nhân vật

## 🔧 Triển khai kỹ thuật

### 1. Input Mapping
Đã thêm action `toggle_map` vào `project.godot`:
```gdscript
toggle_map={
"deadzone": 0.5,
"events": [Object(InputEventKey, keycode=77)] # M key
}
```

### 2. SceneManager Enhancements
**File**: `ui/scripts/scene_manager.gd`

**Tính năng mới**:
- Scene stack management để track scene trước đó
- `toggle_map_scene()`: Logic toggle chính
- `open_map_scene()`: Mở MapScene và lưu scene hiện tại
- `close_map_scene()`: Đóng MapScene và quay về scene cũ
- `goto_scene_without_loading()`: Chuyển scene nhanh không loading screen

**Variables**:
```gdscript
var _scene_stack: Array[String] = []        # Stack lưu các scene
var _map_scene_path: String = "res://maps/New_loor/scenes/MapScene.tscn"
var _is_map_scene_open: bool = false        # Trạng thái MapScene
```

### 3. MapToggleManager Autoload
**File**: `systems/map_toggle_manager.gd`

**Chức năng**:
- Global input handler cho phím M
- Kiểm tra forbidden scenes (menu, loading screen)
- Gọi SceneManager để thực hiện toggle

**Process Mode**: `PROCESS_MODE_ALWAYS` để hoạt động ngay cả khi pause

### 4. MapScene Input Handling
**File**: `maps/New_loor/scripts/MapScene.gd`

**Tính năng mới**:
- Xử lý phím M để đóng MapScene
- `get_viewport().set_input_as_handled()` để ngăn input lan truyền

## 🏗️ Kiến trúc hệ thống

```
MapToggleManager (Autoload)
    ↓ (Global M key detection)
SceneManager (Autoload)
    ↓ (Scene stack management)
MapScene.gd
    ↓ (Local M key handling)
Individual Map Scenes
```

## 🔄 Flow hoạt động

### Mở MapScene:
1. Người chơi nhấn M ở map bất kỳ
2. MapToggleManager detect input
3. SceneManager lưu scene hiện tại vào stack
4. Chuyển đến MapScene (không loading screen)

### Đóng MapScene:
1. Người chơi nhấn M trong MapScene
2. MapScene.gd detect input
3. SceneManager lấy scene từ stack
4. Quay về scene trước đó (không loading screen)

## 🚫 Giới hạn và bảo vệ

### Forbidden Scenes:
- `Home/scenes/Startmenu.tscn` (Main menu)
- `ui/scenes/loading_screen.tscn` (Loading screen)

### Error Handling:
- Kiểm tra stack empty trước khi pop
- Validation scene path trước khi load
- Proper input handling để tránh conflicts

## 🧪 Testing Instructions

### Test cơ bản:
1. Mở bất kỳ map nào (lang_van_lang, doi_tre, etc.)
2. Nhấn M → MapScene mở
3. Nhấn M trong MapScene → Quay về map cũ
4. Kiểm tra UI tên map vẫn hiển thị đúng

### Test edge cases:
1. Nhấn M nhiều lần liên tiếp
2. Mở MapScene từ nhiều map khác nhau
3. Test với loading screens
4. Test với menu scenes

## 📝 Debug Information

Console sẽ hiển thị:
- `🗺️ Phím M được nhấn - Toggle MapScene`
- `📚 Đã lưu scene hiện tại vào stack: [path]`
- `🗺️ Đã mở MapScene`
- `↩️ Đã quay về scene: [path]`

## 🔧 Troubleshooting

### MapScene không mở:
- Check console cho forbidden scene warnings
- Verify MapToggleManager autoload loaded
- Check input map configuration

### Không quay được về scene cũ:
- Check scene stack không empty
- Verify scene paths valid
- Check SceneManager.close_map_scene() calls

### Input conflicts:
- Ensure MapScene.gd handles input properly
- Check input propagation với set_input_as_handled()

## 🚀 Future Enhancements

1. **Animation transitions** cho smooth toggle
2. **Multiple MapScene support** cho khác nhau areas
3. **Breadcrumb navigation** cho complex scene hierarchies
4. **Save/restore scene state** khi toggle

---

**Tác giả**: GitHub Copilot  
**Ngày tạo**: Hôm nay  
**Version**: 1.0  
**Compatible với**: Godot 4.3+
