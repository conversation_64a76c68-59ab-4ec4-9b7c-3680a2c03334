# COMPREHENSIVE TELEPORT DEBUG SCRIPT
extends Node

# Attach this to the Lang Van Lang scene to debug teleport issues

func _ready():
	print("🔍 === COMPREHENSIVE TELEPORT DEBUGGER STARTED ===")
	call_deferred("start_debugging")

func start_debugging():
	await get_tree().process_frame
	
	print("\n🎮 CURRENT SCENE ANALYSIS:")
	var current_scene = get_tree().current_scene
	if current_scene and is_instance_valid(current_scene):
		print("Scene: %s" % current_scene.name)
		var scene_path = current_scene.scene_file_path if current_scene.scene_file_path else "No path"
		print("Scene Path: %s" % scene_path)
	else:
		print("❌ Current scene not available")
		return
	
	debug_player_status()
	debug_scenemanager_status()
	debug_map_controller_status()
	debug_teleport_gates()
	
	print("\n🎯 TELEPORT DEBUG CONTROLS:")
	print("F1: Show this debug info again")
	print("F2: Test manual teleport to dong_dau")
	print("F3: Test manual teleport to rung_nuong")
	print("F4: Debug spawn position system")

func debug_player_status():
	print("\n👤 PLAYER STATUS:")
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("✅ Player found: %s" % player.name)
		print("   Position: %s" % player.global_position)
		print("   Visible: %s" % player.visible)
		print("   In group 'player': %s" % player.is_in_group("player"))
		print("   Process mode: %s" % player.process_mode)
		print("   Parent: %s" % player.get_parent().name)
	else:
		print("❌ Player NOT FOUND!")
		
		# Alternative searches
		player = get_tree().current_scene.find_child("Player", true, false)
		if player:
			print("🔍 Found via find_child: %s" % player.name)
		else:
			print("❌ Player not found via find_child either")

func debug_scenemanager_status():
	print("\n🌐 SCENEMANAGER STATUS:")
	if SceneManager:
		print("✅ SceneManager exists")
		print("   Has spawn position: %s" % SceneManager.has_next_spawn_position())
		if SceneManager.has_next_spawn_position():
			print("   ⚠️ WARNING: Spawn position exists but should be consumed by map controller!")
		print("   Available methods:")
		print("     - goto_scene: %s" % SceneManager.has_method("goto_scene"))
		print("     - set_next_spawn_position: %s" % SceneManager.has_method("set_next_spawn_position"))
		print("     - has_next_spawn_position: %s" % SceneManager.has_method("has_next_spawn_position"))
		print("     - get_next_spawn_position: %s" % SceneManager.has_method("get_next_spawn_position"))
	else:
		print("❌ SceneManager not found!")

func debug_map_controller_status():
	print("\n🗺️ MAP CONTROLLER STATUS:")
	var controllers = get_tree().get_nodes_in_group("map_controllers")
	if controllers.size() > 0:
		print("✅ Map controllers found: %d" % controllers.size())
		for controller in controllers:
			print("   Controller: %s" % controller.name)
			if controller.has_method("_auto_fix_teleport_position"):
				print("     ✅ Has auto-positioning method")
			else:
				print("     ❌ Missing auto-positioning method")
	else:
		print("❌ No map controllers in group 'map_controllers'")
		
		# Search for specific controllers
		var lang_controller = get_tree().current_scene.find_child("LangVanLangMapController", true, false)
		if lang_controller:
			print("🔍 Found LangVanLangMapController via find_child")
		else:
			print("❌ LangVanLangMapController not found")

func debug_teleport_gates():
	print("\n🚪 TELEPORT GATES STATUS:")
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	if gates.size() > 0:
		print("✅ Teleport gates found: %d" % gates.size())
		for gate in gates:
			print("   Gate: %s" % gate.name)
			if gate.has_method("debug_info"):
				gate.debug_info()
			else:
				print("     ID: %s" % gate.gate_id)
				print("     Target: %s" % gate.target_scene)
				print("     Position: %s" % gate.target_position)
	else:
		print("❌ No teleport gates found")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter - Show debug again
		call_deferred("start_debugging")
	elif event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				call_deferred("start_debugging")
			KEY_F2:
				test_manual_teleport_dong_dau()
			KEY_F3:
				test_manual_teleport_rung_nuong()
			KEY_F4:
				debug_spawn_system()

func test_manual_teleport_dong_dau():
	print("\n🧪 TESTING MANUAL TELEPORT TO DONG DAU")
	
	# Set spawn position
	if SceneManager:
		SceneManager.set_next_spawn_position(Vector2(-1200, -429))
		print("📍 Set spawn position: Vector2(-1200, -429)")
		
		# Teleport
		SceneManager.goto_scene("res://maps/dong_dau/scenes/dong_dau.tscn")
	else:
		print("❌ Cannot test - SceneManager not available")

func test_manual_teleport_rung_nuong():
	print("\n🧪 TESTING MANUAL TELEPORT TO RUNG NUONG")
	
	# Set spawn position
	if SceneManager:
		SceneManager.set_next_spawn_position(Vector2(2200, -1200))
		print("📍 Set spawn position: Vector2(2200, -1200)")
		
		# Teleport
		SceneManager.goto_scene("res://maps/rung_nuong/scenes/rung_nuong.tscn")
	else:
		print("❌ Cannot test - SceneManager not available")

func debug_spawn_system():
	print("\n🎯 SPAWN SYSTEM DEBUG:")
	
	var player = get_tree().get_first_node_in_group("player")
	if not player:
		print("❌ No player to test with")
		return
	
	print("Current player position: %s" % player.global_position)
	
	# Test spawn position setting
	if SceneManager:
		print("Testing spawn position system...")
		SceneManager.set_next_spawn_position(Vector2(1000, -1000))
		print("Set test spawn position: Vector2(1000, -1000)")
		print("Has spawn position: %s" % SceneManager.has_next_spawn_position())
		
		var retrieved_pos = SceneManager.get_next_spawn_position()
		print("Retrieved spawn position: %s" % retrieved_pos)
		print("Has spawn position after retrieval: %s" % SceneManager.has_next_spawn_position())
		
		# Test manual player positioning
		print("Moving player to test position...")
		player.global_position = Vector2(500, -500)
		print("Player moved to: %s" % player.global_position)
	else:
		print("❌ SceneManager not available for testing")
