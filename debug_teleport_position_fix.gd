extends Node2D

# Debug script để kiểm tra vấn đề teleport position
# Chạy script này để test teleport từ map kh<PERSON>c sang Dong Dau

func _ready():
	print("🔍 DEBUG: Teleport Position Fix Test")
	print("=====================================")
	
	# Test 1: Kiểm tra các managers
	_test_managers()
	
	# Test 2: Kiểm tra position mapping
	_test_position_mapping()
	
	# Test 3: Simulate teleport to Dong Dau
	_test_teleport_to_dong_dau()

func _test_managers():
	print("\n📋 TEST 1: Manager Availability")
	print("--------------------------------")
	
	# Check SceneManager
	var scene_manager = get_node_or_null("/root/SceneManager")
	if scene_manager:
		print("✅ SceneManager available")
		print("   - has_next_spawn_position: %s" % scene_manager.has_method("has_next_spawn_position"))
		print("   - set_next_spawn_position: %s" % scene_manager.has_method("set_next_spawn_position"))
	else:
		print("❌ SceneManager not available")
	
	# Check TeleportContextManager
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if context_manager:
		print("✅ TeleportContextManager available")
		print("   - has_active_teleport_context: %s" % context_manager.has_method("has_active_teleport_context"))
		print("   - get_spawn_position_for_current_context: %s" % context_manager.has_method("get_spawn_position_for_current_context"))
	else:
		print("❌ TeleportContextManager not available")
	
	# Check TeleportPositionMapping
	var position_mapping = get_node_or_null("/root/TeleportPositionMapping")
	if position_mapping:
		print("✅ TeleportPositionMapping available")
		print("   - get_accurate_spawn_position: %s" % position_mapping.has_method("get_accurate_spawn_position"))
	else:
		print("❌ TeleportPositionMapping not available")

func _test_position_mapping():
	print("\n🗺️ TEST 2: Position Mapping")
	print("----------------------------")
	
	var position_mapping = get_node_or_null("/root/TeleportPositionMapping")
	if not position_mapping:
		print("❌ TeleportPositionMapping not available")
		return
	
	# Test mapping từ các map khác sang Dong Dau
	var test_routes = [
		{"from": "lang_van_lang", "to": "dong_dau"},
		{"from": "rung_nuong", "to": "dong_dau"},
		{"from": "hang_an", "to": "dong_dau"},
		{"from": "suoi_thieng", "to": "dong_dau"},
		{"from": "doi_tre", "to": "dong_dau"}
	]
	
	for route in test_routes:
		var position = position_mapping.get_accurate_spawn_position(route.from, route.to)
		print("   %s → %s: %s" % [route.from, route.to, position])

func _test_teleport_to_dong_dau():
	print("\n🚀 TEST 3: Simulate Teleport to Dong Dau")
	print("------------------------------------------")
	
	# Simulate setting spawn position
	var scene_manager = get_node_or_null("/root/SceneManager")
	if scene_manager:
		var test_position = Vector2(-1421, -429)  # Dong Dau default position
		scene_manager.set_next_spawn_position(test_position)
		print("✅ Set spawn position: %s" % test_position)
		
		# Check if position was set
		if scene_manager.has_next_spawn_position():
			var retrieved_position = scene_manager.get_next_spawn_position()
			print("✅ Retrieved spawn position: %s" % retrieved_position)
		else:
			print("❌ Spawn position not set properly")
	else:
		print("❌ SceneManager not available for testing")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running tests...")
		_test_managers()
		_test_position_mapping()
		_test_teleport_to_dong_dau() 