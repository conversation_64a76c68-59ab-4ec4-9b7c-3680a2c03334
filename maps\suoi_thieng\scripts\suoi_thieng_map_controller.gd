# SuoiThiengMapController.gd - Controller cho map <PERSON><PERSON><PERSON>
extends Node2D
class_name Su<PERSON><PERSON>hiengMapController

# ----- Signals -----
signal map_loaded
signal teleport_gate_activated(gate_id: String)

# ----- Node References -----
@onready var player: Player = null
@onready var teleport_gates: Array[TeleportGate] = []

# ----- Map Settings -----
var map_id: String = "suoi_thieng"
var map_name: String = "Suối Thiêng"

func _ready() -> void:
	# Add to map controllers group for debugging
	add_to_group("map_controllers")
	
	print("💧 <PERSON>oi Thieng Map Controller initialized")
	call_deferred("_setup_map")

func _setup_map() -> void:
	# Tìm player trong scene
	_find_player()
	
	# Check if player needs to be repositioned (for teleport)
	_check_and_setup_teleport_spawn()
	
	# Tìm và setup các cổng dịch chuyển
	_setup_teleport_gates()
	
	# Emit signal map đã load xong
	map_loaded.emit()
	print("✅ <PERSON><PERSON> map setup completed")
	
	# Hiển thị tên bản đồ
	_show_map_name_ui()

func _show_map_name_ui() -> void:
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name(map_name)
		GlobalMapNameUI.show_map_name()
		print("🗺️ Updated global map name UI: %s" % map_name)
	else:
		print("⚠️ GlobalMapNameUI not available")

func _find_player() -> void:
	# Tìm player node
	player = get_tree().get_first_node_in_group("player")
	if not player:
		# Thử tìm theo đường dẫn
		player = get_node_or_null("Player")
	
	if player:
		print("👤 Player found in Suoi Thieng map: %s" % player.name)
		print("Player position: ", player.global_position)
		
		# Auto-fix player position if came from teleport
		_auto_fix_teleport_position()
	else:
		print("⚠️ WARNING: Player not found in Suoi Thieng map")

func _check_and_setup_teleport_spawn() -> void:
	# Check if player needs to be repositioned (for teleport)
	_auto_fix_teleport_position()

func _auto_fix_teleport_position() -> void:
	"""Tự động sửa vị trí player nếu đến từ teleport"""
	if not player:
		return
	
	# Check if SceneManager has spawn position set
	if SceneManager and SceneManager.has_next_spawn_position():
		var target_pos = SceneManager.get_next_spawn_position()
		print("🎯 Auto-fixing player position from %s to %s" % [player.global_position, target_pos])
		
		# Set player position
		player.global_position = target_pos
		
		print("✅ Player repositioned successfully to: %s" % player.global_position)
	else:
		print("📍 No teleport spawn position set, keeping default position")

func _setup_teleport_gates() -> void:
	# Tìm tất cả cổng dịch chuyển trong scene
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	for gate in gates:
		if gate is TeleportGate:
			teleport_gates.append(gate)
			_connect_gate_signals(gate)
			print("Connected teleport gate: ", gate.gate_id)

func _connect_gate_signals(gate: TeleportGate) -> void:
	if not gate:
		return
	
	# Kết nối signals của cổng
	if not gate.player_entered_gate.is_connected(_on_player_entered_gate):
		gate.player_entered_gate.connect(_on_player_entered_gate)
	
	if not gate.player_exited_gate.is_connected(_on_player_exited_gate):
		gate.player_exited_gate.connect(_on_player_exited_gate)

func _on_player_entered_gate(gate: TeleportGate) -> void:
	print("Player entered teleport gate: ", gate.gate_id)
	emit_signal("teleport_gate_activated", gate.gate_id)

func _on_player_exited_gate(gate: TeleportGate) -> void:
	print("Player exited teleport gate: ", gate.gate_id)

# Hàm để thêm cổng dịch chuyển mới
func add_teleport_gate(gate: TeleportGate) -> void:
	if gate and not teleport_gates.has(gate):
		teleport_gates.append(gate)
		_connect_gate_signals(gate)
		
		# Thêm vào group để dễ tìm kiếm
		if not gate.is_in_group("teleport_gates"):
			gate.add_to_group("teleport_gates")
		
		print("Added new teleport gate: ", gate.gate_id)

# Hàm để lấy thông tin các cổng dịch chuyển
func get_teleport_gates() -> Array[TeleportGate]:
	return teleport_gates

func get_teleport_gate_by_id(gate_id: String) -> TeleportGate:
	for gate in teleport_gates:
		if gate.gate_id == gate_id:
			return gate
	return null
