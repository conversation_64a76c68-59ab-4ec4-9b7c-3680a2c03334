[gd_scene load_steps=2 format=3 uid="uid://b8kqy2x3nwx8r"]

[ext_resource type="Script" path="res://Test&debug/validation/comprehensive_teleport_fix_test.gd" id="1_xyz89"]

[node name="ComprehensiveTeleportFixTest" type="Node2D"]
script = ExtResource("1_xyz89")

[node name="InfoLabel" type="Label" parent="."]
offset_left = 10.0
offset_top = 10.0
offset_right = 800.0
offset_bottom = 150.0
text = "🎯 COMPREHENSIVE TELEPORT FIX TEST

Test toàn diện gi<PERSON>i pháp fix lỗi teleport position.

Controls:
- Enter: Chạy comprehensive test
- Space: Debug current state

Test sẽ verify:
✓ System availability (autoloads)
✓ Context management 
✓ Position mapping integration
✓ Real teleport scenarios

Xem Console để theo dõi kết quả chi tiết."
