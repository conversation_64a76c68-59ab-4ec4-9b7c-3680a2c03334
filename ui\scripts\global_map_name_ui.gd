# Global Map Name UI Manager - <PERSON><PERSON><PERSON>n lý UI tên bản đồ cố định
extends Node

var map_name_ui: CanvasLayer = null
var map_name_label: Label = null

func _ready():
	# Tạo UI cố định ngay khi game khởi động
	print("🔧 GlobalMapNameUI _ready() - Starting initialization...")
	call_deferred("_create_persistent_map_ui")  # S<PERSON> dụng call_deferred để đảm bảo scene tree đã sẵn sàng
	print("🗺️ Global Map Name UI Manager initialization queued")

func _create_persistent_map_ui():
	"""Tạo UI tên bản đồ cố định không bao giờ bị xóa"""
	if map_name_ui:
		print("🔧 UI already exists, skipping creation")
		return  # Đã có rồi
	
	print("🔧 Creating new persistent map UI...")
	
	# Tạo CanvasLayer
	map_name_ui = CanvasLayer.new()
	map_name_ui.name = "PersistentMapNameUI"
	map_name_ui.layer = 100  # Layer cao để hiển thị trên cùng
	get_tree().root.add_child(map_name_ui)
	print("🔧 CanvasLayer created with layer: %s" % map_name_ui.layer)
	
	# Tạo Label
	map_name_label = Label.new()
	map_name_label.name = "MapNameLabel"
	map_name_label.text = "🗺️ Loading..."
	
	# Thiết lập style với màu sắc nổi bật hơn
	map_name_label.add_theme_font_size_override("font_size", 36)  # Tăng size
	map_name_label.add_theme_color_override("font_color", Color(1, 1, 0.2, 1))  # Màu vàng sáng
	map_name_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 1))  # Shadow đậm hơn
	map_name_label.add_theme_constant_override("shadow_offset_x", 3)
	map_name_label.add_theme_constant_override("shadow_offset_y", 3)
	
	# Định vị ở góc trên bên phải - sử dụng Control constants
	map_name_label.set_anchors_preset(Control.PRESET_TOP_RIGHT)
	map_name_label.grow_horizontal = Control.GROW_DIRECTION_BEGIN
	
	# Offset từ góc phải trên - điều chỉnh để nhìn rõ hơn
	map_name_label.offset_left = -350   # Cách lề phải 350px
	map_name_label.offset_top = 10      # Cách lề trên 10px
	map_name_label.offset_right = -10   # Cách lề phải 10px
	map_name_label.offset_bottom = 50   # Chiều cao 40px
	
	map_name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	map_name_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	
	# Đảm bảo luôn hiển thị
	map_name_label.modulate.a = 1.0
	map_name_label.visible = true
	
	map_name_ui.add_child(map_name_label)
	
	print("📌 Persistent Map Name UI created and positioned at top-right")
	print("📍 Label position: offset_left=%s, offset_top=%s, offset_right=%s, offset_bottom=%s" % [map_name_label.offset_left, map_name_label.offset_top, map_name_label.offset_right, map_name_label.offset_bottom])

func set_map_name(new_map_name: String):
	"""Cập nhật tên bản đồ - UI sẽ luôn hiển thị"""
	print("🔧 set_map_name called with: %s" % new_map_name)
	
	if not map_name_label:
		print("⚠️ map_name_label is null, creating UI...")
		_create_persistent_map_ui()
	
	if map_name_label:
		map_name_label.text = "🗺️ " + new_map_name
		map_name_label.modulate.a = 1.0  # Đảm bảo luôn hiển thị
		map_name_label.visible = true    # Đảm bảo visible
		
		# Hiệu ứng nhấp nháy nhẹ khi thay đổi
		var tween = create_tween()
		tween.tween_property(map_name_label, "modulate", Color(1.3, 1.3, 0.5, 1), 0.3)
		tween.tween_property(map_name_label, "modulate", Color.WHITE, 0.4)
		
		print("🗺️ Map name updated to: '%s' (always visible)" % new_map_name)
		print("📍 UI Status: visible=%s, modulate=%s" % [map_name_label.visible, map_name_label.modulate])
	else:
		print("❌ Failed to create map_name_label!")

func show_map_name():
	"""Hiện UI (luôn hiển thị)"""
	if map_name_label:
		map_name_label.visible = true
		map_name_label.modulate.a = 1.0
		print("👁️ Map name UI shown")

func is_map_name_visible() -> bool:
	"""Kiểm tra UI có đang hiển thị không"""
	return map_name_label != null and map_name_label.visible

func get_current_map_name() -> String:
	"""Lấy tên map hiện tại"""
	if map_name_label:
		return map_name_label.text.replace("🗺️ ", "")
	return ""

# Hàm debug để test UI
func debug_ui_info():
	"""In thông tin debug về UI"""
	print("=== GlobalMapNameUI Debug Info ===")
	print("map_name_ui: %s" % map_name_ui)
	print("map_name_label: %s" % map_name_label)
	
	if map_name_ui:
		print("CanvasLayer layer: %s" % map_name_ui.layer)
		print("CanvasLayer children count: %s" % map_name_ui.get_child_count())
	
	if map_name_label:
		print("Label text: '%s'" % map_name_label.text)
		print("Label visible: %s" % map_name_label.visible)
		print("Label modulate: %s" % map_name_label.modulate)
		print("Label position: (%s, %s)" % [map_name_label.global_position.x, map_name_label.global_position.y])
		print("Label size: %s" % map_name_label.size)
		print("Label anchors: left=%s, top=%s, right=%s, bottom=%s" % [map_name_label.anchor_left, map_name_label.anchor_top, map_name_label.anchor_right, map_name_label.anchor_bottom])
		print("Label offsets: left=%s, top=%s, right=%s, bottom=%s" % [map_name_label.offset_left, map_name_label.offset_top, map_name_label.offset_right, map_name_label.offset_bottom])
	print("================================")
