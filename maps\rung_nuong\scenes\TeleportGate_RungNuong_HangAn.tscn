[gd_scene load_steps=4 format=3 uid="uid://d9f2h6j8k1mno"]

[ext_resource type="Script" path="res://maps/scripts/teleport_gate.gd" id="1_teleport"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(50, 100)

[sub_resource type="LabelSettings" id="LabelSettings_1"]
font_size = 14

[node name="TeleportGate_RungNuong_HangAn" type="Area2D"]
script = ExtResource("1_teleport")
target_scene = "res://maps/hang_an/scenes/hang_an.tscn"
target_position = Vector2(300, -1900)
gate_id = "rung_nuong_to_hang_an"
activation_delay = 0.5
auto_teleport = false
interaction_key = "teleport_interact"
gate_color = Color(0.9, 0.6, 0.2, 0.7)
activated_color = Color(1, 0.8, 0.4, 0.9)
disabled_color = Color(0.5, 0.5, 0.5, 0.3)
gate_size = Vector2(50, 100)
enable_particles = true
enable_sound = true
enable_screen_shake = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Visual" type="ColorRect" parent="."]
z_index = -1
offset_left = -25.0
offset_top = -50.0
offset_right = 25.0
offset_bottom = 50.0
color = Color(0.9, 0.6, 0.2, 0.7)

[node name="TeleportParticles" type="CPUParticles2D" parent="."]
position = Vector2(0, -20)
emitting = true
amount = 50
lifetime = 2.0
texture = null
emission = {
"shape": 1,
"sphere_radius": 30.0
}
direction = Vector3(0, -1, 0)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector3(0, -98, 0)
scale_amount_min = 0.5
scale_amount_max = 1.5
color = Color(0.9, 0.6, 0.2, 0.8)

[node name="ActivationUI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_left = -75.0
offset_top = -120.0
offset_right = 75.0
offset_bottom = -80.0

[node name="ActivationLabel" type="Label" parent="ActivationUI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Đang kích hoạt..."
label_settings = SubResource("LabelSettings_1")
horizontal_alignment = 1
vertical_alignment = 1

[node name="InteractionPrompt" type="Label" parent="."]
offset_left = -80.0
offset_top = 85.0
offset_right = 80.0
offset_bottom = 110.0
text = "Nhấn [M] để đến Hang Ăn"
label_settings = SubResource("LabelSettings_1")
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="area_exited" from="." to="." method="_on_area_exited"]
[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
