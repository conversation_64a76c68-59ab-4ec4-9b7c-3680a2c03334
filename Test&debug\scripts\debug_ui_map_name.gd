extends Node

func _ready():
	print("🔍 Debugging Map Name UI")
	
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI autoload is available")
		
		# Check if UI elements exist
		if GlobalMapNameUI.map_name_ui:
			print("✅ UI container exists")
		else:
			print("❌ UI container does not exist")
			print("🔧 Creating UI container...")
			GlobalMapNameUI._create_persistent_map_ui()
		
		# Check if label exists
		if GlobalMapNameUI.map_name_label:
			print("✅ Label exists")
			print("   Current text: " + GlobalMapNameUI.map_name_label.text)
			print("   Visibility: " + str(GlobalMapNameUI.map_name_label.visible))
			print("   Opacity: " + str(GlobalMapNameUI.map_name_label.modulate.a))
			
			# Try to make it visible
			GlobalMapNameUI.map_name_label.text = "🗺️ TEST MAP NAME"
			GlobalMapNameUI.map_name_label.modulate.a = 1.0
			GlobalMapNameUI.map_name_label.visible = true
		else:
			print("❌ Label does not exist")
	else:
		print("❌ GlobalMapNameUI autoload is not available")

func _process(_delta):
	if Input.is_action_just_pressed("ui_accept"):
		if GlobalMapNameUI and GlobalMapNameUI.map_name_label:
			# Toggle visibility as test
			GlobalMapNameUI.map_name_label.visible = !GlobalMapNameUI.map_name_label.visible
			print("🔄 Toggled map name visibility: " + str(GlobalMapNameUI.map_name_label.visible))
