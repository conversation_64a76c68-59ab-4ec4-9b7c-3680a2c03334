# Báo Cáo Sửa Lỗi Hệ Thống Teleport và UI Map Name

## Vấn đề được báo cáo
1. **Không hiện tên map ở góc phải màn hình**
2. **Nhấn Enter không dịch chuyển được**
3. **Khi teleport từ dong_dau đến doi_tre chỉ hiện map màu xám/trống**

## Nguyên nhân và cách khắc phục

### 1. Lỗi không hiện tên map
**Nguyên nhân:**
- Hàm `show_map_name()` của `GlobalMapNameUI` không được gọi sau khi dịch chuyển

**Giải pháp:**
- Đảm bảo mỗi map controller (đặc biệt là doi_tre_map_controller.gd) gọi `GlobalMapNameUI.show_map_name()` trong hàm `_ready()` của nó
- Map controller đã có code này, nhưng cần đảm bảo nó được thực thi

### 2. Lỗi nhấn Enter không dịch chuyển
**Nguyên nhân:**
- UID không khớp trong scene doi_tre.tscn khi tham chiếu đến TeleportGate_DoiTre.tscn
- Trong doi_tre.tscn: `uid="uid://bvbqn8k2lyark"`
- Trong TeleportGate_DoiTre.tscn thực tế: `uid="uid://bvbqn8k2lx9rk"`

**Giải pháp:**
- Đã cập nhật UID trong doi_tre.tscn để khớp với UID thực tế trong TeleportGate_DoiTre.tscn

### 3. Lỗi map màu xám/trống khi teleport từ dong_dau đến doi_tre
**Nguyên nhân:**
- Lỗi UID như đã nêu ở trên làm cho TileMap không load đúng
- TileMapLayer trong doi_tre.tscn có thể không được cấu hình đúng

**Giải pháp:**
- Đã cập nhật UID của teleport gate trong doi_tre.tscn
- Đảm bảo mọi tham chiếu đến TileSet đúng với định dạng scene

## Các file đã được sửa
1. `maps/doi_tre/scenes/doi_tre.tscn` - Cập nhật UID teleport gate từ `uid://bvbqn8k2lyark` sang `uid://bvbqn8k2lx9rk`

## Kiểm tra sau khi sửa
- Đảm bảo cổng teleport giữa dong_dau và doi_tre hoạt động đúng
- Kiểm tra UI tên map hiển thị ở góc phải màn hình
- Kiểm tra map doi_tre hiển thị đúng và không còn màn hình xám

## Lưu ý cho phát triển
1. Luôn kiểm tra UIDs trong scene references khi có lỗi liên quan đến teleport
2. Đảm bảo mọi map controller đều gọi `GlobalMapNameUI.show_map_name()` trong hàm `_ready()` để hiển thị tên map
3. Thận trọng khi copy-paste cổng teleport giữa các maps để tránh sai sót về UIDs
