extends Node2D

@onready var info_box = $InfoBox
@onready var map_buttons = [
	$DaoThinhButton,
	$PhungNguyenButton,
	$PhongChauButton,
	$GoMunButton,
	$DongDauButton,
	$NgocLuButton
]

# Texture resources
const RED_BUTTON = preload("res://assets/images/item/red_button.png")
const GREEN_BUTTON = preload("res://assets/images/item/green_button.png")

func _ready():
	print("🗺️ MapScene._ready() called")
	
	for btn in map_buttons:
		if btn != null:
			btn.connect("hovered", _on_button_hovered)
			btn.connect("unhovered", _on_button_unhovered)

			# Kiểm tra trạng thái khóa dựa trên texture
			# Nếu texture là GREEN_BUTTON, thì nút không bị khóa
			if btn.texture_normal == GREEN_BUTTON:
				btn.is_locked = false
			# Nếu texture là RED_BUTTON, thì nút bị khóa
			elif btn.texture_normal == RED_BUTTON:
				btn.is_locked = true

			print("Button ", btn.name, " locked status: ", btn.is_locked)

# Input handler chỉ để đóng MapScene khi nhấn Enter
func _input(event):
	if event.is_action_pressed("toggle_map"):
		print("🗺️ MapScene: Detected Enter key press for closing")
		if SceneManager and SceneManager.is_map_scene_open():
			print("🗺️ MapScene: Calling close_map_scene()")
			SceneManager.close_map_scene()
			# Ngăn input lan truyền đến MapToggleManager
			var viewport = get_viewport()
			if viewport:
				viewport.set_input_as_handled()
		else:
			print("🗺️ MapScene: SceneManager not available or map not open")

# Phím Enter bây giờ được sử dụng cho map toggle thay vì M
# MapToggleManager sẽ handle tất cả Enter key input globally cho map
# func _input(event):
#	# Xử lý phím M để đóng MapScene
#	if event.is_action_pressed("toggle_map"):
#		if SceneManager and SceneManager.is_map_scene_open():
#			SceneManager.close_map_scene()
#			# Kiểm tra viewport tồn tại trước khi gọi set_input_as_handled
#			var viewport = get_viewport()
#			if viewport:
#				viewport.set_input_as_handled() # Ngăn input lan truyền

func _on_button_hovered(title: String, description: String):
	info_box.show_info(title, description)

func _on_button_unhovered():
	info_box.hide_info()

# Hàm để mở khóa button (gọi khi hoàn thành điều kiện nào đó)
func unlock_button(button_name: String) -> void:
	var button = get_node_or_null(button_name)
	if button:
		button.texture_normal = GREEN_BUTTON
		button.is_locked = false
		print("Unlocked button: ", button_name)
