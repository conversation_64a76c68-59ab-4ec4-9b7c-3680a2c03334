extends Node

func _ready():
	print("🧪 Testing GlobalMapNameUI and teleport system...")
	await get_tree().process_frame
	await get_tree().process_frame
	
	# Test GlobalMapNameUI
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI found, creating UI...")
		GlobalMapNameUI._create_persistent_map_ui()
		GlobalMapNameUI.set_map_name("Debug Test Map")
		GlobalMapNameUI.show_map_name()
		GlobalMapNameUI.debug_ui_info()
	else:
		print("❌ ERROR: GlobalMapNameUI not available. Check autoload settings.")
		
	# Test Input Actions
	print("\n📋 Checking input actions:")
	if InputMap.has_action("teleport_interact"):
		print("✅ teleport_interact action exists")
		var events = InputMap.action_get_events("teleport_interact")
		print("   Events: " + str(events.size()))
		for event in events:
			if event is InputEventKey:
				print("   Key: " + str(event.physical_keycode))
	else:
		print("❌ teleport_interact action missing")
		
	if InputMap.has_action("ui_accept"):
		print("✅ ui_accept action exists")
		var events = InputMap.action_get_events("ui_accept")
		print("   Events: " + str(events.size()))
		for event in events:
			if event is InputEventKey:
				print("   Key: " + str(event.physical_keycode))
	else:
		print("❌ ui_accept action missing")
	
	# Print help message
	print("\n❓ Debug help:")
	print("1. Press Enter near a teleport gate to test teleport")
	print("2. Check if map name appears in top-right corner")
	print("3. Check console for debug messages")

func _process(_delta):
	if Input.is_action_just_pressed("ui_accept") or Input.is_action_just_pressed("teleport_interact"):
		print("🔑 Enter key pressed!")
		
	if Input.is_action_just_pressed("ui_cancel"):  # ESC key
		print("📊 DEBUG STATUS:")
		if GlobalMapNameUI:
			GlobalMapNameUI.debug_ui_info()
		else:
			print("❌ GlobalMapNameUI not available")
