# TeleportSystemTester.gd - Comprehensive teleport testing và debugging
extends Node2D

var test_results: Array = []
var current_test_phase: String = ""

func _ready():
	print("🧪 TeleportSystemTester initialized")
	call_deferred("start_comprehensive_test")

func start_comprehensive_test():
	print("\n🧪 === COMPREHENSIVE TELEPORT SYSTEM TEST ===")
	
	# Test 1: Autoload systems
	test_autoload_systems()
	
	# Test 2: Position mapping
	test_position_mapping_system()
	
	# Test 3: Context manager 
	test_context_manager()
	
	# Test 4: Gate functionality
	test_teleport_gates()
	
	# Test 5: Map controllers
	test_map_controllers()
	
	# Display results
	display_test_results()

func test_autoload_systems():
	current_test_phase = "Autoload Systems"
	print("\n📋 Testing Autoload Systems...")
	
	# Test SceneManager
	if SceneManager:
		add_test_result("SceneManager", "PASS", "Available and accessible")
		if SceneManager.has_method("set_next_spawn_position"):
			add_test_result("SceneManager.set_next_spawn_position", "PASS", "Method available")
		else:
			add_test_result("SceneManager.set_next_spawn_position", "FAIL", "Method missing")
	else:
		add_test_result("SceneManager", "FAIL", "Not available")
	
	# Test TeleportPositionMapping
	if TeleportPositionMapping:
		add_test_result("TeleportPositionMapping", "PASS", "Available and accessible")
		if TeleportPositionMapping.has_method("get_accurate_spawn_position"):
			add_test_result("TeleportPositionMapping.get_accurate_spawn_position", "PASS", "Method available")
		else:
			add_test_result("TeleportPositionMapping.get_accurate_spawn_position", "FAIL", "Method missing")
	else:
		add_test_result("TeleportPositionMapping", "FAIL", "Not available")
	
	# Test TeleportContextManager
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if context_manager:
		add_test_result("TeleportContextManager", "PASS", "Available and accessible")
		if context_manager.has_method("set_teleport_context"):
			add_test_result("TeleportContextManager.set_teleport_context", "PASS", "Method available")
		else:
			add_test_result("TeleportContextManager.set_teleport_context", "FAIL", "Method missing")
	else:
		add_test_result("TeleportContextManager", "FAIL", "Not available - need to restart game")

func test_position_mapping_system():
	current_test_phase = "Position Mapping"
	print("\n📋 Testing Position Mapping System...")
	
	if not TeleportPositionMapping:
		add_test_result("Position Mapping", "FAIL", "TeleportPositionMapping not available")
		return
	
	# Test critical mappings
	var test_routes = [
		["lang_van_lang", "dong_dau"],
		["dong_dau", "lang_van_lang"],
		["lang_van_lang", "rung_nuong"],
		["rung_nuong", "lang_van_lang"]
	]
	
	for route in test_routes:
		var from_map = route[0]
		var to_map = route[1]
		var spawn_position = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		
		if spawn_position != Vector2.ZERO:
			add_test_result("Route %s -> %s" % [from_map, to_map], "PASS", "Position: %s" % spawn_position)
		else:
			add_test_result("Route %s -> %s" % [from_map, to_map], "FAIL", "No position mapping found")

func test_context_manager():
	current_test_phase = "Context Manager"
	print("\n📋 Testing Context Manager...")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		add_test_result("Context Manager", "FAIL", "Not available")
		return
	
	# Test context creation
	context_manager.set_teleport_context(
		"test_gate",
		"lang_van_lang",
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	)
	
	if context_manager.validate_context():
		var context = context_manager.get_current_teleport_context()
		add_test_result("Context Creation", "PASS", "Context created with position: %s" % context.target_position)
		
		# Test context retrieval
		var spawn_pos = context_manager.get_spawn_position_for_current_context()
		if spawn_pos != Vector2.ZERO:
			add_test_result("Context Retrieval", "PASS", "Spawn position retrieved: %s" % spawn_pos)
		else:
			add_test_result("Context Retrieval", "FAIL", "No spawn position retrieved")
	else:
		add_test_result("Context Creation", "FAIL", "Invalid context created")

func test_teleport_gates():
	current_test_phase = "Teleport Gates"
	print("\n📋 Testing Teleport Gates...")
	
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	add_test_result("Gates Found", "INFO", "%d gates in scene" % gates.size())
	
	for gate in gates:
		if gate is TeleportGate:
			var gate_name = gate.name
			var gate_id = gate.gate_id
			var target_scene = gate.target_scene
			
			if gate_id != "":
				add_test_result("Gate %s ID" % gate_name, "PASS", "ID: %s" % gate_id)
			else:
				add_test_result("Gate %s ID" % gate_name, "WARN", "No gate_id set")
			
			if target_scene != "":
				if ResourceLoader.exists(target_scene):
					add_test_result("Gate %s Target" % gate_name, "PASS", "Scene exists: %s" % target_scene)
				else:
					add_test_result("Gate %s Target" % gate_name, "FAIL", "Scene missing: %s" % target_scene)
			else:
				add_test_result("Gate %s Target" % gate_name, "FAIL", "No target scene set")

func test_map_controllers():
	current_test_phase = "Map Controllers"
	print("\n📋 Testing Map Controllers...")
	
	var controllers = get_tree().get_nodes_in_group("map_controllers")
	add_test_result("Controllers Found", "INFO", "%d controllers in scene" % controllers.size())
	
	# Test current scene controller
	var current_scene = get_tree().current_scene
	var scene_name = current_scene.name
	
	# Look for map controller in current scene
	var controller = null
	for child in current_scene.get_children():
		if "controller" in child.name.to_lower() or "Controller" in child.name:
			controller = child
			break
	
	if controller:
		add_test_result("Scene Controller", "PASS", "Found: %s" % controller.name)
		
		if controller.has_method("_auto_fix_teleport_position"):
			add_test_result("Controller Auto-fix", "PASS", "_auto_fix_teleport_position method available")
		else:
			add_test_result("Controller Auto-fix", "FAIL", "_auto_fix_teleport_position method missing")
	else:
		add_test_result("Scene Controller", "WARN", "No controller found in %s" % scene_name)

func add_test_result(test_name: String, status: String, detail: String):
	test_results.append({
		"phase": current_test_phase,
		"test": test_name,
		"status": status,
		"detail": detail
	})
	
	var status_icon = "❓"
	match status:
		"PASS": status_icon = "✅"
		"FAIL": status_icon = "❌"
		"WARN": status_icon = "⚠️"
		"INFO": status_icon = "ℹ️"
	
	print("   %s %s: %s" % [status_icon, test_name, detail])

func display_test_results():
	print("\n📊 === TEST SUMMARY ===")
	
	var pass_count = 0
	var fail_count = 0
	var warn_count = 0
	var info_count = 0
	
	for result in test_results:
		match result.status:
			"PASS": pass_count += 1
			"FAIL": fail_count += 1
			"WARN": warn_count += 1
			"INFO": info_count += 1
	
	print("✅ PASS: %d" % pass_count)
	print("❌ FAIL: %d" % fail_count)
	print("⚠️ WARN: %d" % warn_count)
	print("ℹ️ INFO: %d" % info_count)
	print("📋 TOTAL: %d tests" % test_results.size())
	
	if fail_count > 0:
		print("\n🔧 CRITICAL ISSUES FOUND:")
		for result in test_results:
			if result.status == "FAIL":
				print("   ❌ %s: %s" % [result.test, result.detail])
	
	print("\n🎯 RECOMMENDATIONS:")
	_generate_recommendations()

func _generate_recommendations():
	var has_context_manager = get_node_or_null("/root/TeleportContextManager") != null
	var has_position_mapping = TeleportPositionMapping != null
	
	if not has_context_manager:
		print("   1. Restart game để load TeleportContextManager autoload")
	
	if not has_position_mapping:
		print("   2. Kiểm tra TeleportPositionMapping autoload trong project.godot")
	
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	if gates.size() == 0:
		print("   3. Thêm teleport gates vào scene và đảm bảo chúng trong group 'teleport_gates'")
	
	print("   4. Test thực tế bằng cách dịch chuyển giữa các map")

# Debug input
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter
		start_comprehensive_test()
	elif event.is_action_pressed("ui_select"):  # Space
		debug_current_player_position()

func debug_current_player_position():
	print("\n👤 CURRENT PLAYER DEBUG:")
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("   Position: %s" % player.global_position)
		print("   Scene: %s" % get_tree().current_scene.name)
		
		# Test positioning systems
		if SceneManager and SceneManager.has_next_spawn_position():
			print("   ⚠️ SceneManager has unused spawn position: %s" % SceneManager.get_next_spawn_position())
		
		var context_manager = get_node_or_null("/root/TeleportContextManager")
		if context_manager and context_manager.has_active_teleport_context():
			print("   ⚠️ TeleportContextManager has active context")
	else:
		print("   ❌ No player found")
