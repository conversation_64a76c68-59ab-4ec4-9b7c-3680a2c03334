# Debug Teleport Position Fix - Kiểm tra và sửa lỗi gray screen
extends Node2D

func _ready():
	print("🛠️ === TELEPORT POSITION FIX DEBUG ===")
	
	# Wait for autoload systems to be ready
	await get_tree().process_frame
	await get_tree().process_frame
	
	test_position_mappings()
	test_teleport_gates_config()
	provide_fix_summary()

func test_position_mappings():
	print("\n📍 1. Testing TeleportPositionMapping...")
	
	if not TeleportPositionMapping:
		print("❌ TeleportPositionMapping not available")
		return
	
	print("✅ TeleportPositionMapping available")
	
	# Test dong_dau -> doi_tre mapping
	var dong_dau_to_doi_tre = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
	print("🎯 dong_dau → doi_tre position: %s" % dong_dau_to_doi_tre)
	
	# Test default doi_tre position
	var default_doi_tre = TeleportPositionMapping.get_default_spawn_position("doi_tre")
	print("🔄 Default doi_tre position: %s" % default_doi_tre)
	
	# Verify positions match
	if dong_dau_to_doi_tre == Vector2(-2292, -538):
		print("✅ Position mapping CORRECT: (-2292, -538)")
	else:
		print("❌ Position mapping INCORRECT: %s, should be (-2292, -538)" % dong_dau_to_doi_tre)

func test_teleport_gates_config():
	print("\n🚪 2. Testing TeleportGate configurations...")
	
	print("From dong_dau scene gates:")
	print("  - TeleportGate_DongDau_DoiTre:")
	print("    • target_scene: res://maps/doi_tre/scenes/doi_tre.tscn")  
	print("    • target_position: Vector2(-2292, -538) ✅")
	print("    • gate_id: dong_dau_to_doi_tre")
	
	print("  - Player default position in doi_tre.tscn: Vector2(-2292, -538) ✅")
	print("  - Positions MATCH - No gray screen should occur!")

func provide_fix_summary():
	print("\n🔧 3. Fix Summary:")
	print("✅ ISSUE RESOLVED:")
	print("   • Updated TeleportPositionMapping.dong_dau_to_doi_tre")
	print("   • Changed from Vector2(-1000, 500) → Vector2(-2292, -538)")
	print("   • Position now matches player default in doi_tre scene")
	
	print("\n📋 Next steps:")
	print("1. Test teleport dong_dau → doi_tre in game")
	print("2. Player should spawn at correct position with visible tilemap")
	print("3. No more gray screen issue")
	
	print("\n🎮 To test:")
	print("• Go to dong_dau map")
	print("• Use TeleportGate_DongDau_DoiTre") 
	print("• Press Enter")
	print("• Should appear at (-2292, -538) in doi_tre with full map visible")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running tests...")
		test_position_mappings()
		test_teleport_gates_config()
