# Quick UI System Check - PowerShell Version
Write-Host "🔍 Quick UI System Check" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

Write-Host "1. Checking autoload configuration..." -ForegroundColor Yellow
Select-String -Pattern "GlobalMapNameUI" project.godot

Write-Host ""
Write-Host "2. Checking if GlobalMapNameUI script exists..." -ForegroundColor Yellow
if (Test-Path "ui\scripts\global_map_name_ui.gd") {
    Write-Host "✅ GlobalMapNameUI script found" -ForegroundColor Green
} else {
    Write-Host "❌ GlobalMapNameUI script missing" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. Checking map controllers..." -ForegroundColor Yellow
$controllers = Get-ChildItem -Path "maps\*\scripts\*_map_controller.gd" -Recurse | Where-Object { (Get-Content $_.FullName) -like "*GlobalMapNameUI.set_map_name*" }
Write-Host "Controllers using GlobalMapNameUI: $($controllers.Count)" -ForegroundColor Green

Write-Host ""
Write-Host "4. Checking for old UI system usage..." -ForegroundColor Yellow
$oldUsage = Get-ChildItem -Path "maps\*\scripts\*.gd" -Recurse -ErrorAction SilentlyContinue | Where-Object { (Get-Content $_.FullName -ErrorAction SilentlyContinue) -like "*map_name_display.tscn*" }
Write-Host "Controllers using old UI system: $($oldUsage.Count)" -ForegroundColor $(if ($oldUsage.Count -eq 0) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "=========================" -ForegroundColor Cyan
Write-Host "✅ Quick check completed!" -ForegroundColor Green
Write-Host "To test UI:" -ForegroundColor White
Write-Host "1. Open debug_ui_test.tscn in Godot" -ForegroundColor White
Write-Host "2. Run scene (F6)" -ForegroundColor White
Write-Host "3. Check console for debug output" -ForegroundColor White
Write-Host "=========================" -ForegroundColor Cyan
