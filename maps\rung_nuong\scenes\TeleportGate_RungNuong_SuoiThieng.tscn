[gd_scene load_steps=4 format=3 uid="uid://e8j2n4p6q1rvw"]

[ext_resource type="Script" path="res://maps/scripts/teleport_gate.gd" id="1_teleport"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(50, 100)

[sub_resource type="LabelSettings" id="LabelSettings_1"]
font_size = 14

[node name="TeleportGate_RungNuong_SuoiThieng" type="Area2D"]
script = ExtResource("1_teleport")
target_scene = "res://maps/suoi_thieng/scenes/suoi_thieng.tscn"
target_position = Vector2(-2000, 200)
gate_id = "rung_nuong_to_suoi_thieng"
activation_delay = 0.5
auto_teleport = false
interaction_key = "teleport_interact"
gate_color = Color(0.1, 0.6, 0.8, 0.7)
activated_color = Color(0.3, 0.8, 1, 0.9)
disabled_color = Color(0.5, 0.5, 0.5, 0.3)
gate_size = Vector2(50, 100)
enable_particles = true
enable_sound = true
enable_screen_shake = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Visual" type="ColorRect" parent="."]
z_index = -1
offset_left = -25.0
offset_top = -50.0
offset_right = 25.0
offset_bottom = 50.0
color = Color(0.1, 0.6, 0.8, 0.7)

[node name="TeleportParticles" type="CPUParticles2D" parent="."]
emitting = false

[node name="ActivationUI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0

[node name="ActivationLabel" type="Label" parent="ActivationUI"]
layout_mode = 0
offset_left = -25.0
offset_top = -105.0
offset_right = 25.0
offset_bottom = -82.0
text = "Activating..."
horizontal_alignment = 1
label_settings = SubResource("LabelSettings_1")

[node name="InteractionPrompt" type="Label" parent="."]
offset_left = -25.0
offset_top = 85.0
offset_right = 133.0
offset_bottom = 108.0
text = "Nhấn [M] để dịch chuyển"
horizontal_alignment = 1
label_settings = SubResource("LabelSettings_1")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
