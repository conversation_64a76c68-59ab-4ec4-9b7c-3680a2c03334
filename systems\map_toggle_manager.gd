extends Node

# Global input handler for MapScene toggle functionality
# Autoload script để xử lý phím Enter toàn cục cho map toggle

func _ready():
	print("🗺️ MapToggleManager đã được khởi tạo - Sử dụng phím Enter")
	# Đặt process mode để có thể nhận input ngay cả khi game pause
	process_mode = Node.PROCESS_MODE_ALWAYS

func _input(event):
	# CHỈ xử lý phím Enter (phím M được xử lý bởi EnhancedInputManager)
	if event.is_action_pressed("ui_accept") and not event.echo:
		print("🔍 MapToggleManager: Detected Enter key press for map toggle")
		
		# Kiểm tra xem player có đang ở trong teleport gate không
		# Nếu có, ưu tiên teleport thay vì mở map  
		if _is_player_in_teleport_gate():
			print("🚪 Player đang ở trong teleport gate, ưu tiên teleport")
			return  # Không xử lý map toggle, để teleport gate handle
			
		# Kiểm tra xem có đang ở trong MapScene không
		var current_scene = get_tree().current_scene
		if current_scene and (current_scene.name == "MapScene" or current_scene.scene_file_path == "res://maps/New_loor/scenes/MapScene.tscn"):
			print("🗺️ Đang ở trong MapScene, để MapScene tự handle")
			return
			
		handle_map_toggle()
		# Prevent further processing of this input event
		get_viewport().set_input_as_handled()

func handle_map_toggle():
	"""Xử lý logic toggle MapScene"""
	print("🗺️ Phím Enter được nhấn - Toggle MapScene")
	
	# Debug SceneManager state
	if SceneManager:
		print("🔍 Current SceneManager state: is_map_scene_open = %s" % SceneManager.is_map_scene_open())
	else:
		print("⚠️ SceneManager not available")
		return  # Không thể tiếp tục nếu không có SceneManager
	
	# Kiểm tra xem current_scene có tồn tại không
	var current_scene = get_tree().current_scene
	if not current_scene or not is_instance_valid(current_scene):
		print("⚠️ Current scene không tồn tại hoặc không hợp lệ")
		return
	
	print("🔍 Current scene: %s" % current_scene.name)
	
	# Kiểm tra scene_file_path với null safety
	var current_scene_path = ""
	if current_scene.scene_file_path:
		current_scene_path = current_scene.scene_file_path
	print("🔍 Current scene path: '%s'" % current_scene_path)
	
	if current_scene_path == "":
		print("⚠️ Scene không có file path, có thể là scene runtime")
		# Vẫn cho phép toggle nếu không phải forbidden scene
	
	var forbidden_scenes = [
		"res://Home/scenes/Startmenu.tscn",
		"res://ui/scenes/loading_screen.tscn"
	]
	
	# Không cho phép toggle map trong các scene cấm (chỉ khi có path)
	if current_scene_path != "":
		for forbidden_path in forbidden_scenes:
			if current_scene_path == forbidden_path:
				print("🚫 Không thể mở MapScene từ scene: %s" % current_scene_path)
				return
	
	# Debug: check if we're in MapScene
	var is_in_map_scene = current_scene_path == "res://maps/New_loor/scenes/MapScene.tscn" or current_scene.name == "MapScene"
	print("🔍 Is in MapScene: %s" % is_in_map_scene)
	
	# Nếu đang ở trong MapScene, để MapScene tự handle việc đóng
	if is_in_map_scene:
		print("🗺️ Đang ở trong MapScene, MapScene sẽ handle việc đóng")
		return
	
	# Gọi SceneManager để toggle MapScene (chỉ mở)
	if SceneManager:
		# Chỉ mở MapScene nếu chưa mở
		if not SceneManager.is_map_scene_open():
			print("📂 MapToggleManager: Opening MapScene")
			SceneManager.open_map_scene()
		else:
			print("🔍 MapScene đã mở, không cần action từ MapToggleManager")
	else:
		print("❌ SceneManager không tồn tại")

func _is_player_in_teleport_gate() -> bool:
	"""Kiểm tra xem player có đang ở trong teleport gate không"""
	var current_scene = get_tree().current_scene
	if not current_scene:
		return false
	
	# Tìm tất cả TeleportGate trong scene hiện tại
	var teleport_gates = _find_teleport_gates(current_scene)
	
	for gate in teleport_gates:
		if gate.has_method("is_player_inside") and gate.is_player_inside():
			print("🚪 Found player inside teleport gate: %s" % gate.name)
			return true
	
	return false

func _find_teleport_gates(node: Node) -> Array:
	"""Tìm tất cả TeleportGate trong cây node"""
	var gates = []
	
	# Kiểm tra node hiện tại
	if node.get_class() == "Area2D" and node.has_method("_activate_teleport"):
		gates.append(node)
	
	# Tìm trong các child node
	for child in node.get_children():
		gates += _find_teleport_gates(child)
	
	return gates
