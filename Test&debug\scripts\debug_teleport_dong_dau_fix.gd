# Test Script - Debug Teleport Issue từ dong_dau
extends Node

func _ready():
	print("🧪 === TELEPORT DEBUG TEST ===")
	test_scene_paths()
	test_position_mapping()
	test_scene_manager()

func test_scene_paths():
	print("\n📂 Testing Scene Paths:")
	
	var scenes_to_test = [
		"res://maps/doi_tre/scenes/doi_tre.tscn",
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	]
	
	for scene_path in scenes_to_test:
		if ResourceLoader.exists(scene_path):
			print("✅ %s - EXISTS" % scene_path)
			var scene_resource = load(scene_path)
			if scene_resource:
				print("  ✅ Can load resource")
				var instance = scene_resource.instantiate()
				if instance:
					print("  ✅ Can instantiate scene")
					instance.queue_free()
				else:
					print("  ❌ Cannot instantiate scene")
			else:
				print("  ❌ Cannot load resource")
		else:
			print("❌ %s - NOT FOUND" % scene_path)

func test_position_mapping():
	print("\n📍 Testing Position Mapping:")
	
	if TeleportPositionMapping:
		print("✅ TeleportPositionMapping available")
		
		var test_mappings = [
			["dong_dau", "doi_tre"],
			["dong_dau", "lang_van_lang"],
			["doi_tre", "dong_dau"],
			["doi_tre", "lang_van_lang"]
		]
		
		for mapping in test_mappings:
			var from_map = mapping[0]
			var to_map = mapping[1]
			var position = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
			print("  🎯 %s -> %s: %s" % [from_map, to_map, position])
	else:
		print("❌ TeleportPositionMapping not available")

func test_scene_manager():
	print("\n🔄 Testing SceneManager:")
	
	if SceneManager:
		print("✅ SceneManager available")
		print("  Methods: %s" % SceneManager.get_method_list().map(func(m): return m.name))
		
		if SceneManager.has_method("goto_scene"):
			print("  ✅ goto_scene method available")
		else:
			print("  ❌ goto_scene method missing")
			
		if SceneManager.has_method("set_next_spawn_position"):
			print("  ✅ set_next_spawn_position method available")
		else:
			print("  ❌ set_next_spawn_position method missing")
	else:
		print("❌ SceneManager not available")

	print("\n🧪 === TEST COMPLETED ===")
