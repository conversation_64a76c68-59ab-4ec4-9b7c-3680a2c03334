[gd_scene load_steps=4 format=3 uid="uid://byttek652vrgc"]

[ext_resource type="Script" path="res://maps/lang_van_lang/scripts/enemy_spawn_gate.gd" id="1_yvnxs"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(50, 100)

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(0.8, 0.2, 0.2, 1, 0.9, 0.3, 0.3, 0)

[node name="EnemySpawnGate" type="Node2D"]
script = ExtResource("1_yvnxs")

[node name="GateVisual" type="ColorRect" parent="."]
offset_left = -25.0
offset_top = -50.0
offset_right = 25.0
offset_bottom = 50.0
color = Color(0.8, 0.2, 0.2, 0.5)

[node name="SpawnArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 0
monitoring = false
monitorable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="SpawnArea"]
shape = SubResource("RectangleShape2D_1")

[node name="SpawnParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 30
one_shot = true
explosiveness = 0.8
emission_shape = 3
emission_rect_extents = Vector2(20, 40)
direction = Vector2(0, -1)
spread = 30.0
gravity = Vector2(0, 0)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
scale_amount_min = 2.0
scale_amount_max = 5.0
color_ramp = SubResource("Gradient_1")

[node name="Label" type="Label" parent="."]
offset_left = -50.0
offset_top = -70.0
offset_right = 50.0
offset_bottom = -50.0
text = "Enemy Gate"
horizontal_alignment = 1

[node name="Prompt" type="Label" parent="."]
visible = false
offset_left = -60.0
offset_top = 60.0
offset_right = 60.0
offset_bottom = 80.0
text = "Nhấn [M] để dịch chuyển"
horizontal_alignment = 1
