# Enhanced Teleport Gate Fix - Sửa lỗi gray screen hoàn to<PERSON><PERSON>
extends Area2D
class_name EnhancedTeleportGate

signal player_entered_gate(gate: EnhancedTeleportGate)
signal player_exited_gate(gate: EnhancedTeleportGate)

# Settings tương tự TeleportGate gốc
@export var target_scene: String = ""
@export var target_position: Vector2 = Vector2.ZERO
@export var gate_id: String = ""
@export var interaction_key: String = "teleport_interact"

# Internal variables
var _player_inside: bool = false
var _current_player: Player = null
var _is_activated: bool = false

func _ready() -> void:
	if not is_in_group("teleport_gates"):
		add_to_group("teleport_gates")
	
	# Connect signals
	body_entered.connect(_on_body_entered)
	body_exited.connect(_on_body_exited)
	
	print("✅ Enhanced TeleportGate ready: %s" % gate_id)

func _input(event: InputEvent) -> void:
	if not _player_inside or _is_activated:
		return
		
	if event.is_action_pressed(interaction_key) or event.is_action_pressed("ui_accept"):
		_activate_teleport()

func _on_body_entered(body: Node2D) -> void:
	if body is Player:
		_current_player = body as Player
		_player_inside = true
		print("👤 Player entered enhanced teleport gate: %s" % gate_id)
		player_entered_gate.emit(self)

func _on_body_exited(body: Node2D) -> void:
	if body is Player:
		_current_player = null
		_player_inside = false
		print("👤 Player exited enhanced teleport gate: %s" % gate_id)
		player_exited_gate.emit(self)

func _activate_teleport() -> void:
	if _is_activated or target_scene.is_empty():
		return
		
	_is_activated = true
	print("🌀 Enhanced teleport activating to: %s" % target_scene)
	
	# Validate scene exists
	if not ResourceLoader.exists(target_scene):
		push_error("❌ Target scene not found: %s" % target_scene)
		_is_activated = false
		return
	
	# Save accurate spawn position
	if _current_player and TeleportPositionMapping:
		var current_map = _get_current_map_name()
		var target_map = _extract_map_name_from_scene(target_scene)
		
		var accurate_position = TeleportPositionMapping.get_accurate_spawn_position(current_map, target_map)
		if accurate_position == Vector2.ZERO and target_position != Vector2.ZERO:
			accurate_position = target_position
		
		if accurate_position != Vector2.ZERO and SceneManager:
			SceneManager.set_next_spawn_position(accurate_position)
			print("💾 Saved spawn position: %s" % accurate_position)
	
	# Enhanced scene transition with multiple fallbacks
	await _enhanced_scene_transition()

func _enhanced_scene_transition() -> void:
	"""Enhanced scene transition với multiple safety checks"""
	print("🔄 Starting enhanced scene transition...")
	
	# Method 1: SceneManager (preferred)
	if SceneManager and SceneManager.has_method("goto_scene"):
		print("📊 Using SceneManager transition")
		SceneManager.goto_scene(target_scene)
		return
	
	# Method 2: Direct scene change with validation
	print("🔄 Using direct scene change")
	
	# Show simple loading indicator
	_show_simple_loading()
	
	# Wait for stability
	await get_tree().process_frame
	await get_tree().process_frame
	
	# Validate scene one more time
	if not ResourceLoader.exists(target_scene):
		print("❌ Scene validation failed at transition time")
		_is_activated = false
		return
	
	# Use call_deferred for clean transition
	get_tree().call_deferred("change_scene_to_file", target_scene)

func _show_simple_loading() -> void:
	"""Show simple loading overlay"""
	var loading_overlay = ColorRect.new()
	loading_overlay.name = "SimpleLoadingOverlay"
	loading_overlay.color = Color(0, 0, 0, 0.8)
	loading_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_overlay.z_index = 999
	
	var loading_label = Label.new()
	loading_label.text = "🌀 Đang dịch chuyển..."
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_label.add_theme_font_size_override("font_size", 24)
	loading_label.add_theme_color_override("font_color", Color.WHITE)
	
	loading_overlay.add_child(loading_label)
	get_tree().root.add_child(loading_overlay)
	
	# Auto-remove after delay
	get_tree().create_timer(2.0).timeout.connect(loading_overlay.queue_free)

# Helper functions (same as original)
func _get_current_map_name() -> String:
	var current_scene = get_tree().current_scene
	if not current_scene:
		return ""
	
	var scene_path = current_scene.scene_file_path
	if scene_path.is_empty():
		return ""
	
	var scene_name = scene_path.get_file().get_basename()
	var map_names = ["lang_van_lang", "rung_nuong", "dong_dau", "hang_an", "suoi_thieng", "doi_tre"]
	for map_name in map_names:
		if scene_name.contains(map_name):
			return map_name
	return scene_name

func _extract_map_name_from_scene(scene_path: String) -> String:
	if scene_path.is_empty():
		return ""
	
	var scene_name = scene_path.get_file().get_basename()
	var map_names = ["lang_van_lang", "rung_nuong", "dong_dau", "hang_an", "suoi_thieng", "doi_tre"]
	for map_name in map_names:
		if scene_name.contains(map_name):
			return map_name
	return scene_name
