extends CanvasLayer

# UI hiển thị tên bản đồ ở góc trên bên phải
@onready var map_name_label: Label = $MapNameLabel

func _ready():
	# Thiết lập style cho label
	if map_name_label:
		map_name_label.add_theme_font_size_override("font_size", 32)
		map_name_label.add_theme_color_override("font_color", Color(1, 1, 0.7, 1))
		map_name_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.8))
		map_name_label.add_theme_constant_override("shadow_offset_x", 2)
		map_name_label.add_theme_constant_override("shadow_offset_y", 2)
		
		# UI hiển thị cố định - KHÔNG tự động xóa
		map_name_label.modulate.a = 1.0  # Luôn hiển thị
		print("📌 Map Name UI khởi tạo - hiển thị cố định")

# <PERSON><PERSON><PERSON> cập nhật tên bản đồ
func set_map_name(map_name: String):
	if map_name_label:
		map_name_label.text = "🗺️ " + map_name
		print("🗺️ Hiển thị tên bản đồ: ", map_name)
		
		# Hiệu ứng fade in nhẹ khi thay đổi tên map
		var tween = create_tween()
		tween.tween_property(map_name_label, "modulate:a", 1.0, 0.3)
		
		print("📌 UI tên bản đồ sẽ luôn hiển thị cố định")

# Hàm cập nhật tên map mà không có hiệu ứng
func update_map_name_direct(map_name: String):
	if map_name_label:
		map_name_label.text = "🗺️ " + map_name
		map_name_label.modulate.a = 1.0
		print("🗺️ Cập nhật tên bản đồ: ", map_name)
