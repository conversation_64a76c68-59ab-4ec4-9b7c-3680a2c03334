# PlayerSpawnManager.gd - Q<PERSON><PERSON><PERSON> lý vị trí spawn của player khi teleport
extends Node

# Lưu vị trí spawn tiếp theo
var next_spawn_position: Vector2 = Vector2.ZERO
var has_spawn_position: bool = false

# Lưu thông tin player state
var player_data: Dictionary = {}

func set_next_spawn_position(position: Vector2) -> void:
	"""Thiết lập vị trí spawn cho scene tiếp theo"""
	next_spawn_position = position
	has_spawn_position = true
	print("📍 Set next spawn position: %s" % position)

func get_next_spawn_position() -> Vector2:
	"""Lấy vị trí spawn đã được thiết lập"""
	if has_spawn_position:
		var pos = next_spawn_position
		clear_spawn_position()  # Clear after use
		return pos
	return Vector2.ZERO

func clear_spawn_position() -> void:
	"""Xóa vị trí spawn đã lưu"""
	next_spawn_position = Vector2.ZERO
	has_spawn_position = false

func has_next_spawn_position() -> bool:
	"""Kiểm tra có vị trí spawn được thiết lập không"""
	return has_spawn_position

func save_player_state(player: Player) -> void:
	"""Lưu trạng thái player trước khi teleport"""
	if not player:
		return
	
	player_data = {
		"position": player.global_position,
		"health": player.health if player.has_method("get_health") else 100,
		"facing_direction": player.facing_direction if "facing_direction" in player else 1,
		# Thêm các thuộc tính khác nếu cần
	}
	print("💾 Saved player state: %s" % player_data)

func restore_player_state(player: Player) -> void:
	"""Khôi phục trạng thái player sau khi teleport"""
	if not player or player_data.is_empty():
		return
	
	# Chỉ restore những thuộc tính có thể restore an toàn
	if player_data.has("health") and player.has_method("set_health"):
		player.set_health(player_data.health)
	
	if player_data.has("facing_direction") and player.has_method("set_facing_direction"):
		player.set_facing_direction(player_data.facing_direction)
	
	print("🔄 Restored player state")
	player_data.clear()

func setup_player_spawn(player: Player) -> void:
	"""Thiết lập vị trí spawn cho player sau khi load scene"""
	if not player:
		print("⚠️ PlayerSpawnManager: Player không tồn tại")
		return
	
	if has_next_spawn_position():
		var spawn_pos = get_next_spawn_position()
		print("🎯 Moving player to spawn position: %s" % spawn_pos)
		
		# Đợi một frame để đảm bảo player đã sẵn sàng
		await player.get_tree().process_frame
		
		player.global_position = spawn_pos
		print("✅ Player moved to: %s" % player.global_position)
		
		# Đảm bảo camera cập nhật
		if player.get_viewport():
			var camera = player.get_viewport().get_camera_2d()
			if camera:
				camera.force_update_scroll()
	else:
		print("📍 No spawn position set, player stays at default position")

# Utility function để tìm player trong scene hiện tại
func find_player_in_current_scene() -> Player:
	"""Tìm player trong scene hiện tại"""
	var player = null
	
	# Method 1: Group
	var players = get_tree().get_nodes_in_group("player")
	if players.size() > 0:
		player = players[0]
	
	# Method 2: Direct search
	if not player:
		player = get_tree().current_scene.find_child("Player", true, false)
	
	# Method 3: Class-based search
	if not player:
		var all_nodes = get_tree().get_nodes_in_group("all")
		for node in all_nodes:
			if node is Player:
				player = node
				break
	
	return player

func auto_setup_player_on_scene_load() -> void:
	"""Tự động setup player khi scene được load"""
	await get_tree().process_frame  # Đợi scene load xong
	
	var player = find_player_in_current_scene()
	if player:
		setup_player_spawn(player)
	else:
		print("⚠️ PlayerSpawnManager: Không tìm thấy player trong scene")
