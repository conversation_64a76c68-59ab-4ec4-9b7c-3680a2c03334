# TeleportContextManager.gd - Quản lý ngữ cảnh teleport hoàn chỉnh
extends Node

# Lư<PERSON> thông tin ngữ cảnh teleport hiện tại
var current_teleport_context: Dictionary = {}

# C<PERSON>u trúc dữ liệu đ<PERSON>y đủ cho teleport context
var teleport_context_template = {
	"caller_gate_id": "",           # ID của cổng xuất phát
	"caller_map": "",               # Tên map xuất phát  
	"target_scene": "",             # Scene đích
	"target_map": "",               # Tên map đích
	"target_position": Vector2.ZERO, # Vị trí spawn chính xác
	"spawn_method": "",             # Phương pháp xác định spawn position
	"timestamp": 0                  # Thời điểm teleport
}

func _ready():
	print("🎯 TeleportContextManager initialized")

# ====== CORE TELEPORT CONTEXT MANAGEMENT ======

func set_teleport_context(gate_id: String, current_map: String, target_scene: String) -> void:
	"""Thiết lập ngữ cảnh teleport hoàn chỉnh"""
	var target_map = _extract_map_name_from_scene(target_scene)
	
	# Tạo context mới
	current_teleport_context = {
		"caller_gate_id": gate_id,
		"caller_map": current_map,
		"target_scene": target_scene,
		"target_map": target_map,
		"target_position": Vector2.ZERO,
		"spawn_method": "",
		"timestamp": Time.get_unix_time_from_system()
	}
	
	# Xác định vị trí spawn chính xác
	_determine_accurate_spawn_position()
	
	print("🎯 Teleport context set: %s (%s) -> %s (%s)" % [
		current_map, gate_id, target_map, current_teleport_context.target_position
	])
	
	# Lưu vào SceneManager để map controller có thể truy cập
	if SceneManager:
		SceneManager.set_next_spawn_position(current_teleport_context.target_position)

func _determine_accurate_spawn_position() -> void:
	"""Xác định vị trí spawn chính xác dựa trên context"""
	var from_map = current_teleport_context.caller_map
	var to_map = current_teleport_context.target_map
	var gate_id = current_teleport_context.caller_gate_id
	
	var spawn_position = Vector2.ZERO
	var method = ""
	
	# Phương pháp 1: Sử dụng TeleportPositionMapping (đã được cập nhật với vị trí gate chính xác)
	if TeleportPositionMapping:
		spawn_position = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		if spawn_position != Vector2.ZERO:
			method = "TeleportPositionMapping (accurate gate positioning)"
			print("🎯 Using updated position mapping: %s -> %s = %s" % [from_map, to_map, spawn_position])
	
	# Phương pháp 3: Sử dụng vị trí mặc định của map đích
	if spawn_position == Vector2.ZERO and TeleportPositionMapping:
		spawn_position = TeleportPositionMapping.get_default_spawn_position(to_map)
		if spawn_position != Vector2.ZERO:
			method = "Default map position"
	
	# Phương pháp 4: Hardcoded fallback
	if spawn_position == Vector2.ZERO:
		var default_positions = {
			"lang_van_lang": Vector2(300, -1900),
			"rung_nuong": Vector2(753, -1225),
			"dong_dau": Vector2(-1421, -429),
			"hang_an": Vector2(-2069, 484),
			"suoi_thieng": Vector2(-2069, 484),
			"doi_tre": Vector2(-2292, -538)
		}
		if default_positions.has(to_map):
			spawn_position = default_positions[to_map]
			method = "Hardcoded fallback"
	
	# Cập nhật context
	current_teleport_context.target_position = spawn_position
	current_teleport_context.spawn_method = method
	
	print("🎯 Spawn position determined: %s via %s" % [spawn_position, method])

func get_current_teleport_context() -> Dictionary:
	"""Lấy ngữ cảnh teleport hiện tại"""
	return current_teleport_context

func has_active_teleport_context() -> bool:
	"""Kiểm tra có ngữ cảnh teleport đang hoạt động không"""
	return not current_teleport_context.is_empty() and current_teleport_context.get("target_position", Vector2.ZERO) != Vector2.ZERO

func clear_teleport_context() -> void:
	"""Xóa ngữ cảnh teleport sau khi đã sử dụng"""
	print("🧹 Clearing teleport context")
	current_teleport_context.clear()

func get_spawn_position_for_current_context() -> Vector2:
	"""Lấy vị trí spawn cho ngữ cảnh hiện tại và clear context"""
	if has_active_teleport_context():
		var position = current_teleport_context.target_position
		clear_teleport_context()
		return position
	return Vector2.ZERO

# ====== UTILITY FUNCTIONS ======

func _extract_map_name_from_scene(scene_path: String) -> String:
	"""Trích xuất tên map từ đường dẫn scene"""
	var parts = scene_path.split("/")
	for part in parts:
		if part.ends_with(".tscn"):
			return part.replace(".tscn", "")
	
	# Fallback: get last folder name
	if parts.size() >= 2:
		return parts[parts.size() - 2]
	
	return "unknown_map"

func debug_current_context() -> void:
	"""Debug thông tin context hiện tại"""
	print("\n🔍 === TELEPORT CONTEXT DEBUG ===")
	if current_teleport_context.is_empty():
		print("❌ No active teleport context")
	else:
		for key in current_teleport_context.keys():
			print("   %s: %s" % [key, current_teleport_context[key]])
	print("=================================\n")

# ====== INTEGRATION HELPERS ======

func create_context_from_gate(gate: TeleportGate, current_scene_name: String) -> void:
	"""Tạo context từ TeleportGate"""
	if not gate:
		print("❌ Cannot create context from null gate")
		return
	
	set_teleport_context(
		gate.gate_id,
		current_scene_name,
		gate.target_scene
	)

# ====== ADVANCED DEBUG & UTILITY FUNCTIONS ======

func get_context_summary() -> String:
	"""Trả về summary ngắn gọn của context hiện tại"""
	if current_teleport_context.is_empty():
		return "No active context"
	
	return "%s (%s) -> %s at %s" % [
		current_teleport_context.get("caller_map", "unknown"),
		current_teleport_context.get("caller_gate_id", "unknown"),
		current_teleport_context.get("target_map", "unknown"),
		current_teleport_context.get("target_position", Vector2.ZERO)
	]

func force_clear_context() -> void:
	"""Force clear context (để debug hoặc emergency cleanup)"""
	print("🚨 Force clearing teleport context")
	current_teleport_context.clear()

func update_context_position(new_position: Vector2, reason: String = "") -> void:
	"""Cập nhật vị trí trong context hiện tại"""
	if current_teleport_context.is_empty():
		print("⚠️ Cannot update position - no active context")
		return
	
	current_teleport_context.target_position = new_position
	current_teleport_context.spawn_method = "Manual update: " + reason
	print("🔄 Context position updated to %s (%s)" % [new_position, reason])

func has_valid_position() -> bool:
	"""Kiểm tra context có vị trí hợp lệ không"""
	return has_active_teleport_context() and current_teleport_context.target_position != Vector2.ZERO

func validate_context() -> bool:
	"""Kiểm tra tính hợp lệ của context hiện tại"""
	if current_teleport_context.is_empty():
		return false
	
	var required_fields = ["caller_gate_id", "caller_map", "target_scene", "target_map", "target_position"]
	for field in required_fields:
		if not current_teleport_context.has(field):
			print("❌ Invalid context: missing field %s" % field)
			return false
		
		# Kiểm tra riêng cho từng loại field
		if field == "target_position":
			var pos_value = current_teleport_context[field]
			if pos_value is Vector2 and pos_value == Vector2.ZERO:
				print("❌ Invalid context: target_position is zero")
				return false
		else:
			# Các field khác là String
			var str_value = current_teleport_context[field]
			if str_value is String and str_value == "":
				print("❌ Invalid context: empty %s" % field)
				return false
	
	return true
