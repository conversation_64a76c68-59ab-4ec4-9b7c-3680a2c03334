# Test script để kiểm tra input mapping M vs Enter
extends Node

func _ready():
	print("🧪 === TEST INPUT MAPPING FIX ===")
	print("📋 Testing M key vs Enter key mapping for MapScreen")
	print("")
	test_input_mappings()
	print("")
	test_teleport_map_conflict()
	print("")
	print("✅ Test hoàn tất! Kiểm tra log để xem kết quả.")

func test_input_mappings():
	print("🔍 1. KIỂM TRA INPUT ACTION MAPPINGS:")
	
	# Kiểm tra toggle_map (M key)
	if InputMap.has_action("toggle_map"):
		var events = InputMap.action_get_events("toggle_map")
		for event in events:
			if event is InputEventKey:
				print("  📌 toggle_map: physical_keycode=%d (M=%d)" % [event.physical_keycode, 77])
	
	# Kiểm tra ui_accept (Enter key)  
	if InputMap.has_action("ui_accept"):
		var events = InputMap.action_get_events("ui_accept")
		for event in events:
			if event is InputEventKey:
				print("  📌 ui_accept: physical_keycode=%d (Enter=%d)" % [event.physical_keycode, 4194309])
	
	# <PERSON><PERSON><PERSON> tra teleport_interact (Enter key)
	if InputMap.has_action("teleport_interact"):
		var events = InputMap.action_get_events("teleport_interact")
		for event in events:
			if event is InputEventKey:
				print("  📌 teleport_interact: physical_keycode=%d (Enter=%d)" % [event.physical_keycode, 4194309])

func test_teleport_map_conflict():
	print("🔍 2. KIỂM TRA CONFLICT RESOLUTION:")
	
	# Kiểm tra MapToggleManager
	var map_toggle_manager = get_node("/root/MapToggleManager")
	if map_toggle_manager:
		print("  ✅ MapToggleManager exists và đang sử dụng Enter key")
		print("  📋 Logic: Enter key sẽ ưu tiên teleport nếu player ở trong gate")
	else:
		print("  ❌ MapToggleManager not found")
	
	# Kiểm tra TeleportPositionMapping
	var teleport_mapping = get_node("/root/TeleportPositionMapping")
	if teleport_mapping:
		print("  ✅ TeleportPositionMapping exists")
	else:
		print("  ❌ TeleportPositionMapping not found")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("🔑 Enter key pressed - sẽ được xử lý bởi priority system")
	elif event.is_action_pressed("toggle_map"):
		print("🔑 M key pressed - chỉ dùng fallback nếu cần")
