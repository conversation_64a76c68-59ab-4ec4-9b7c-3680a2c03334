# Comprehensive Input System Test Script
extends Node

# Script để test đ<PERSON>y đủ input system mới

var test_results = []

func _ready():
	print("🧪 =====COMPREHENSIVE INPUT SYSTEM TEST STARTED===== 🧪")
	await get_tree().create_timer(1.0).timeout
	_run_all_tests()

func _run_all_tests():
	print("\n🧪 RUNNING ALL INPUT SYSTEM TESTS...")
	
	# Test 1: Check autoload presence
	await _test_autoload_systems()
	
	# Test 2: Check EnhancedInputManager functionality
	await _test_enhanced_input_manager()
	
	# Test 3: Check TeleportGate input integration
	await _test_teleport_gate_input_integration()
	
	# Test 4: Check MapToggleManager compatibility
	await _test_map_toggle_compatibility()
	
	# Test 5: Simulate input scenarios
	await _test_input_scenarios()
	
	await get_tree().create_timer(1.0).timeout
	_print_final_results()

func _test_autoload_systems():
	print("\n📋 TEST 1: AUTOLOAD SYSTEMS CHECK")
	var test_name = "Autoload Systems Check"
	
	# Check các autoload systems
	var autoloads_to_check = [
		"SceneManager",
		"TeleportPositionMapping", 
		"GlobalMapNameUI",
		"MapToggleManager",
		"EnhancedInputManager"
	]
	
	var success_count = 0
	
	for autoload_name in autoloads_to_check:
		if get_node("/root/" + autoload_name):
			print("✅ %s: LOADED" % autoload_name)
			success_count += 1
		else:
			print("❌ %s: NOT FOUND" % autoload_name)
	
	var success = success_count == autoloads_to_check.size()
	test_results.append({
		"name": test_name,
		"success": success,
		"details": "%d/%d autoloads loaded" % [success_count, autoloads_to_check.size()]
	})

func _test_enhanced_input_manager():
	print("\n📋 TEST 2: ENHANCED INPUT MANAGER FUNCTIONALITY")
	var test_name = "Enhanced Input Manager Functionality"
	
	var enhanced_input = get_node("/root/EnhancedInputManager")
	if not enhanced_input:
		test_results.append({
			"name": test_name,
			"success": false,
			"details": "EnhancedInputManager not found"
		})
		return
	
	print("✅ EnhancedInputManager found")
	
	# Check methods exist
	var methods_to_check = [
		"_handle_map_toggle",
		"_is_near_teleport_gate",
		"_handle_map_toggle_logic"
	]
	
	var methods_exist = 0
	for method_name in methods_to_check:
		if enhanced_input.has_method(method_name):
			print("✅ Method %s: EXISTS" % method_name)
			methods_exist += 1
		else:
			print("❌ Method %s: NOT FOUND" % method_name)
	
	var success = methods_exist == methods_to_check.size()
	test_results.append({
		"name": test_name,
		"success": success,
		"details": "%d/%d methods found" % [methods_exist, methods_to_check.size()]
	})

func _test_teleport_gate_input_integration():
	print("\n📋 TEST 3: TELEPORT GATE INPUT INTEGRATION")
	var test_name = "TeleportGate Input Integration"
	
	# Try to find any teleport gates in the scene
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	if gates.size() == 0:
		print("⚠️ No teleport gates found in current scene")
		test_results.append({
			"name": test_name,
			"success": true,
			"details": "No gates to test (expected in some scenes)"
		})
		return
	
	print("🔍 Found %d teleport gate(s)" % gates.size())
	
	var compatible_gates = 0
	for gate in gates:
		if gate.has_method("get_player_inside_state"):
			print("✅ Gate %s: Has get_player_inside_state() method" % gate.name)
			compatible_gates += 1
		else:
			print("❌ Gate %s: Missing get_player_inside_state() method" % gate.name)
	
	var success = compatible_gates == gates.size()
	test_results.append({
		"name": test_name,
		"success": success,
		"details": "%d/%d gates compatible" % [compatible_gates, gates.size()]
	})

func _test_map_toggle_compatibility():
	print("\n📋 TEST 4: MAP TOGGLE COMPATIBILITY")
	var test_name = "MapToggle Compatibility"
	
	var scene_manager = get_node("/root/SceneManager")
	if not scene_manager:
		test_results.append({
			"name": test_name,
			"success": false,  
			"details": "SceneManager not found"
		})
		return
	
	# Check SceneManager methods
	var required_methods = [
		"is_map_scene_open",
		"open_map_scene"
	]
	
	var methods_found = 0
	for method_name in required_methods:
		if scene_manager.has_method(method_name):
			print("✅ SceneManager.%s(): EXISTS" % method_name)
			methods_found += 1
		else:
			print("❌ SceneManager.%s(): NOT FOUND" % method_name)
	
	var success = methods_found == required_methods.size()
	test_results.append({
		"name": test_name,
		"success": success,
		"details": "%d/%d SceneManager methods found" % [methods_found, required_methods.size()]
	})

func _test_input_scenarios():
	print("\n📋 TEST 5: INPUT SCENARIOS SIMULATION")
	var test_name = "Input Scenarios Simulation"
	
	# Scenario 1: Check current scene compatibility
	var current_scene = get_tree().current_scene
	if not current_scene:
		test_results.append({
			"name": test_name,
			"success": false,
			"details": "No current scene found"
		})
		return
	
	print("🔍 Current scene: %s" % current_scene.name)
	print("🔍 Scene path: %s" % current_scene.scene_file_path)
	
	# Check forbidden scenes
	var forbidden_scenes = [
		"res://Home/scenes/Startmenu.tscn",
		"res://ui/scenes/loading_screen.tscn"
	]
	
	var is_forbidden = false
	for forbidden_path in forbidden_scenes:
		if current_scene.scene_file_path == forbidden_path:
			is_forbidden = true
			break
	
	print("🔍 Scene forbidden for map toggle: %s" % is_forbidden)
	
	# Scenario 2: Check player presence
	var player = get_tree().get_first_node_in_group("player")
	var has_player = player != null
	print("🔍 Player found: %s" % has_player)
	
	# Scenario 3: Check teleport gates
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	print("🔍 Teleport gates found: %d" % gates.size())
	
	var success = true  # Scenarios are informational
	test_results.append({
		"name": test_name,
		"success": success,
		"details": "Scene: %s, Player: %s, Gates: %d" % [current_scene.name, has_player, gates.size()]
	})

func _print_final_results():
	print("\n🧪 =====COMPREHENSIVE INPUT SYSTEM TEST RESULTS===== 🧪")
	
	var total_tests = test_results.size()
	var passed_tests = 0
	
	for result in test_results:
		var status = "✅ PASS" if result.success else "❌ FAIL"
		print("%s %s: %s" % [status, result.name, result.details])
		if result.success:
			passed_tests += 1
	
	print("\n📊 SUMMARY: %d/%d tests passed (%.1f%%)" % [passed_tests, total_tests, (float(passed_tests)/total_tests)*100])
	
	if passed_tests == total_tests:
		print("🎉 ALL TESTS PASSED! Input system is ready!")
	else:
		print("⚠️ Some tests failed. Please check the issues above.")
	
	print("🧪 =====COMPREHENSIVE INPUT SYSTEM TEST COMPLETED===== 🧪\n")

# Helper method to manually trigger test from console
func run_manual_test():
	_run_all_tests()
