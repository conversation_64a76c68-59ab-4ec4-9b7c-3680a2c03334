# 🧪 Test & Debug Structure

Th<PERSON> mục này chứa tất cả file test và debug của project, đ<PERSON><PERSON><PERSON> tổ chức theo chức năng:

## 📁 **scripts/**
GDScript files để test và debug:
- `debug_gray_screen_fix.gd` - Debug gray screen teleport issue
- `debug_teleport_dong_dau_fix.gd` - Debug teleport từ dong_dau
- `debug_teleport_input.gd` - Debug input mapping
- `debug_teleport_position_fix.gd` - Debug position spawning
- `debug_ui_and_input_test.gd` - Test UI và input system
- `debug_ui_map_name.gd` - Debug map name display
- `debug_ui_test.gd` - General UI testing
- `gray_screen_fix_test.gd` - Gray screen issue testing
- `test_map_name_timer.gd` - Test map name timer
- `test_ui_debug.gd` - UI debug functions
- `test_ui_teleport.gd` - Teleport UI testing
- ✨ `teleport_system_comprehensive_fix.gd` - **NEW** Complete teleport system fix
- ✨ `final_system_validation.gd` - **NEW** Final system validation script

## 📁 **scenes/**
Scene files (.tscn) cho testing:
- `debug_teleport_position_fix.tscn`
- `debug_ui_and_input_test.tscn`
- `debug_ui_map_name.tscn`
- `debug_ui_test.tscn`
- `test_ui_debug.tscn`
- ✨ `teleport_system_comprehensive_fix.tscn` - **NEW** Comprehensive teleport fix scene
- ✨ `complete_system_validation.tscn` - **NEW** Full system validation scene
- ✨ `final_system_validation.tscn` - **NEW** Final validation with all systems

## 📁 **validation/**
Scripts để validate hệ thống:
- `input_validation_test.gd` - Validate input mapping
- `quick_gray_screen_validation.gd` - Quick gray screen checks
- `system_validation_test.gd` - Overall system validation
- ✨ `complete_system_validation.gd` - **NEW** Comprehensive system validation
- ✨ `final_system_validation.gd` - **NEW** Final validation with all 5 objectives

## 📁 **utilities/**
Scripts và tools để hỗ trợ development:
- `quick_ui_check.ps1` - PowerShell script check UI  
- `quick_ui_check.sh` - Bash script check UI
- `simple_update.ps1` - Update script
- `update_teleport_gates.ps1` - Update teleport gates
- `update_ui_and_teleport_docs.ps1` - Update documentation
- ✨ `quick_goals_check_fixed.ps1` - **NEW** Fixed PowerShell validation script

---

## 🎯 **TESTING GUIDE - TELEPORT & UI FIXES**

### **✅ Problem 1: dong_dau → doi_tre Teleport Issue**

**Test Steps:**
1. Load `dong_dau.tscn` scene
2. Move player to TeleportGate_DongDau_DoiTre
3. Press **Enter** key
4. Verify smooth transition to doi_tre map
5. Check player spawns at correct position: `Vector2(-2292, -538)`

**Validation Script:** `Test&debug/scripts/teleport_system_comprehensive_fix.gd`

### **✅ Problem 2: Map Name Display**

**Test Steps:**
1. Start any gameplay scene
2. Check top-right corner of screen
3. Verify map name displays with format: `🗺️ [Map Name]`
4. Change scenes via teleport
5. Confirm map name updates automatically

**Expected Results:**
- Lang Van Lang: `🗺️ Làng Văn Lang`
- Dong Dau: `🗺️ Đồng Đậu` 
- Doi Tre: `🗺️ Đồi Tre`
- Hang An: `🗺️ Hang Ăn`

### **✅ Problem 3: Enter Key for MapScene**

**Important:** System uses **M key** (not Enter) for MapScene toggle!

**Test Steps:**
1. Load any gameplay scene
2. Press **M** key (toggle_map action)
3. Verify MapScene opens
4. Press **M** again in MapScene to close
5. Confirm return to previous scene

**Key Mappings:**
- **Enter** (teleport_interact): Teleport through gates
- **M** (toggle_map): Open/close MapScene

### **✅ Problem 4: Safe Scene Loading**

**Test Steps:**
1. Run validation script: `Test&debug/validation/complete_system_validation.gd`
2. Check console for scene validation results
3. All critical scenes should show: `✅ Exists and loadable`
4. No error messages should appear

**Validation Covers:**
- Scene file existence
- Resource loading capability  
- Position mapping accuracy
- Input action availability

### **✅ Problem 5: Player Position Accuracy**

**Test All Routes:**
```
dong_dau → doi_tre: Vector2(-2292, -538)
doi_tre → dong_dau: Vector2(-1421, -429)  
lang_van_lang → hang_an: Vector2(-2069, 484)
hang_an → lang_van_lang: Vector2(300, -1900)
```

**Quick Test:** Load `complete_system_validation.tscn` and check console output.

---

## 🛠️ **DEBUGGING COMMANDS**

### **Run Complete System Test:**
```gdscript
# In Godot debug console:
get_tree().change_scene_to_file("res://Test&debug/scenes/complete_system_validation.tscn")
```

### **Test Specific Teleport Fix:**
```gdscript  
# In Godot debug console:
get_tree().change_scene_to_file("res://Test&debug/scenes/teleport_system_comprehensive_fix.tscn")
```

### **Manual Validation:**
```gdscript
# Check autoloads
print("SceneManager: ", SceneManager != null)
print("TeleportPositionMapping: ", TeleportPositionMapping != null) 
print("GlobalMapNameUI: ", GlobalMapNameUI != null)

# Test position mapping
if TeleportPositionMapping:
    print("dong_dau→doi_tre: ", TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre"))
```

---

## 🎮 **USER ACCEPTANCE TESTING**

### **Scenario 1: Normal Gameplay Flow**
1. Start game → Map name visible ✅
2. Press M → MapScene opens ✅  
3. Navigate around → UI persists ✅
4. Find teleport gate → Enter prompt shows ✅
5. Press Enter → Smooth teleport ✅
6. New map loads → Name updates ✅

### **Scenario 2: Error Handling**
1. Invalid scene path → Error message shown ✅
2. Missing target file → Graceful failure ✅
3. Corrupted scene → No crash ✅

### **Scenario 3: Performance**
1. Multiple teleports → No memory leaks ✅
2. UI updates → Smooth animations ✅
3. Scene transitions → Fast loading ✅

---
*Updated: August 1, 2025 - All fixes implemented and tested*