# Git Branch Strategy - Du An Game Nam Quoc Son Ha

## Cấu trúc nh<PERSON> ch<PERSON>h

### 1. **master** 
- <PERSON><PERSON><PERSON><PERSON>, chứa code ổn định và đã được test
- Chỉ merge từ nhánh `release` hoặc `hotfix`
- Mỗi commit trên master tương ứng với một version release

### 2. **develop**
- <PERSON><PERSON><PERSON><PERSON> phát triển chính
- Tích hợp tất cả các feature mới
- Base cho tất cả feature branches
- Merge vào `release` khi chuẩn bị phát hành

### 3. **release/v1.0**
- <PERSON>h<PERSON>h chuẩn bị release version 1.0
- Bug fixes và polish cuối cùng
- Không thêm feature mới
- Merge vào `master` và `develop`

### 4. **hotfix/critical-bugs**
- Sửa các bug nghiêm trọng trên production
- <PERSON> từ `master`
- <PERSON><PERSON> vào cả `master` và `develop`

## Feature Branches

### Core Systems

#### **feature/player-system**
- <PERSON><PERSON> thống nhân vật chính
- Files liên quan:
  - `player/scripts/player.gd`
  - `states/` (tất cả player states)
  - `player/player.tscn`
- Chức năng: Movement, animations, state machine, camera

#### **feature/combat-system**
- Hệ thống chiến đấu
- Files liên quan:
  - `abilities/` (toàn bộ thư mục)
  - `states/attack_state.gd`
  - Combat mechanics
- Chức năng: Abilities, combos, damage calculation

#### **feature/enemy-ai**
- Hệ thống kẻ địch và AI
- Files liên quan:
  - `enemy/` (toàn bộ thư mục)
  - `systems/quest/enemy_wave_spawner.gd`
- Chức năng: Enemy AI, spawning, behavior patterns

#### **feature/inventory-system**
- Hệ thống kho đồ và vật phẩm
- Files liên quan:
  - `Items/` (toàn bộ thư mục)
  - `systems/inventory/`
- Chức năng: Weapons, equipment, potions, drop system

### Game Systems

#### **feature/quest-mission**
- Hệ thống nhiệm vụ
- Files liên quan:
  - `systems/mission/`
  - `systems/quest/`
  - Quest UI components
- Chức năng: Quest management, objectives, rewards

#### **feature/ranking-xp**
- Hệ thống kinh nghiệm và cấp bậc
- Files liên quan:
  - `systems/ranking/`
  - `systems/xp/`
  - `ranks/`
- Chức năng: XP gain, level up, ranking system

#### **feature/ui-hud**
- Giao diện người dùng
- Files liên quan:
  - `ui/` (toàn bộ thư mục)
  - `hud_progress/`
- Chức năng: HUD, menus, inventory UI, dialogs

#### **feature/map-levels**
- Hệ thống bản đồ và levels
- Files liên quan:
  - `maps/` (toàn bộ thư mục)
- Chức năng: Level design, map navigation, scene transitions

#### **feature/npc-dialog**
- Hệ thống NPC và hội thoại
- Files liên quan:
  - `npcs/` (toàn bộ thư mục)
- Chức năng: NPC interactions, dialog system

### Additional Features

#### **feature/ally-system**
- Hệ thống đồng minh
- Files liên quan:
  - `allies/` (toàn bộ thư mục)
- Chức năng: Ally AI, following behavior

#### **feature/animal-system**
- Hệ thống động vật
- Files liên quan:
  - `animals/` (toàn bộ thư mục)
- Chức năng: Animal behaviors, interactions

#### **feature/audio-effects**
- Hệ thống âm thanh và hiệu ứng
- Files liên quan:
  - `effects/`
  - Audio assets
- Chức năng: Sound effects, music, visual effects

#### **feature/save-load-system**
- Hệ thống lưu/tải game
- Files liên quan:
  - Save system scripts
  - Game state management
- Chức năng: Save progress, load game, persistent data

#### **feature/performance-optimization**
- Tối ưu hóa hiệu suất
- Files liên quan:
  - Optimization scripts
  - Resource management
- Chức năng: Performance improvements, memory optimization

## Quy trình làm việc

### 1. Tạo feature mới
```bash
git checkout develop
git pull origin develop
git checkout -b feature/ten-feature
# Làm việc trên feature
git add .
git commit -m "Add feature: mô tả"
git push origin feature/ten-feature
```

### 2. Merge feature
```bash
# Tạo Pull Request từ feature -> develop
# Sau khi review và approve:
git checkout develop
git merge feature/ten-feature
git branch -d feature/ten-feature
```

### 3. Chuẩn bị release
```bash
git checkout develop
git checkout -b release/v1.1
# Bug fixes và final polish
git checkout master
git merge release/v1.1
git tag v1.1
git checkout develop
git merge release/v1.1
```

### 4. Hotfix
```bash
git checkout master
git checkout -b hotfix/fix-critical-bug
# Fix bug
git checkout master
git merge hotfix/fix-critical-bug
git checkout develop
git merge hotfix/fix-critical-bug
```

## Lưu ý quan trọng

1. **Luôn branch từ develop** cho features mới
2. **Test kỹ** trước khi merge vào develop
3. **Code review** bắt buộc cho tất cả pull requests
4. **Commit messages** phải rõ ràng và mô tả đúng thay đổi
5. **Conflict resolution** phải được handle cẩn thận
6. **Backup** thường xuyên và push lên remote

## Team Assignment Suggestions

- **Player System**: Developer chuyên về character controller
- **Combat System**: Developer chuyên về gameplay mechanics  
- **Enemy AI**: Developer chuyên về AI programming
- **UI/HUD**: Designer/Developer chuyên về UI/UX
- **Map/Levels**: Level designer + developer
- **Audio/Effects**: Sound designer + effects programmer
