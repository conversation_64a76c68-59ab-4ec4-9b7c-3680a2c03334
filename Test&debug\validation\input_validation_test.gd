extends Node

# Script để kiểm tra tất cả input actions trong project

func _ready():
	print("🔍 === INPUT VALIDATION TEST ===")
	validate_all_actions()

func validate_all_actions():
	var required_actions = [
		"toggle_map",
		"teleport_activate", 
		"teleport_interact",
		"jump",
		"move_left",
		"move_right",
		"move_down",
		"run",
		"attack_1",
		"attack_2",
		"attack_3",
		"attack_4",
		"burst",
		"health_potion",
		"mana_potion"
	]
	
	print("\n📋 Checking required actions:")
	for action in required_actions:
		if InputMap.has_action(action):
			var events = InputMap.action_get_events(action)
			if events.size() > 0:
				var key_info = ""
				for event in events:
					if event is InputEventKey:
						key_info += "Key: %s (code: %s) " % [event.as_text(), event.physical_keycode]
				print("✅ %s: %s" % [action, key_info])
			else:
				print("⚠️ %s: Action exists but no events mapped" % action)
		else:
			print("❌ %s: Missing action!" % action)
	
	print("\n🗺️ Map Toggle Analysis:")
	if InputMap.has_action("toggle_map"):
		var events = InputMap.action_get_events("toggle_map")
		for event in events:
			if event is InputEventKey:
				var key_name = get_key_name(event.physical_keycode)
				print("Map toggle sử dụng: %s (keycode: %s)" % [key_name, event.physical_keycode])
	
	print("\n🚪 Teleport Actions Analysis:")
	for action in ["teleport_activate", "teleport_interact"]:
		if InputMap.has_action(action):
			var events = InputMap.action_get_events(action)
			for event in events:
				if event is InputEventKey:
					var key_name = get_key_name(event.physical_keycode)
					print("%s sử dụng: %s (keycode: %s)" % [action, key_name, event.physical_keycode])

func get_key_name(keycode: int) -> String:
	match keycode:
		77: return "M"
		32: return "Space"
		4194309: return "Enter"
		65: return "A"
		68: return "D"
		83: return "S"
		_: return "Unknown(%s)" % keycode
