extends Node2D

# Comprehensive Teleport System Test
# This script will be used to test and validate the teleport system

@onready var result_label = $UI/ResultsLabel

func _ready():
	print("🧪 Starting teleport system test...")
	call_deferred("run_tests")

func run_tests():
	await get_tree().process_frame
	await get_tree().process_frame
	
	test_scene_manager()
	test_teleport_position_mapping()
	test_player_positioning()
	
	print("🎯 All teleport tests completed!")

func test_scene_manager():
	print("\n🔄 Testing SceneManager:")
	result_label.text = "Testing SceneManager..."
	
	if not SceneManager:
		print("❌ SceneManager not available!")
		result_label.text += "\n❌ SceneManager not available!"
		return
	
	print("✅ SceneManager available")
	result_label.text += "\n✅ SceneManager available"
	
	# Test spawn position methods
	print("Testing spawn position methods...")
	var test_position = Vector2(100, 100)
	SceneManager.set_next_spawn_position(test_position)
	
	if SceneManager.has_next_spawn_position():
		print("✅ set_next_spawn_position worked")
		result_label.text += "\n✅ set_next_spawn_position worked"
		
		var retrieved_position = SceneManager.get_next_spawn_position()
		if retrieved_position == test_position:
			print("✅ get_next_spawn_position returned correct position")
			result_label.text += "\n✅ get_next_spawn_position returned correct position"
		else:
			print("❌ get_next_spawn_position returned wrong position")
			result_label.text += "\n❌ get_next_spawn_position returned wrong position"
			print("Expected: %s, Got: %s" % [test_position, retrieved_position])
			
		# Verify position was cleared after get
		if not SceneManager.has_next_spawn_position():
			print("✅ Position correctly cleared after get")
			result_label.text += "\n✅ Position correctly cleared after get"
		else:
			print("❌ Position not cleared after get")
			result_label.text += "\n❌ Position not cleared after get"
	else:
		print("❌ set_next_spawn_position failed")
		result_label.text += "\n❌ set_next_spawn_position failed"
	
	# Test manual clear
	SceneManager.set_next_spawn_position(test_position)
	SceneManager.clear_next_spawn_position()
	if not SceneManager.has_next_spawn_position():
		print("✅ clear_next_spawn_position worked")
		result_label.text += "\n✅ clear_next_spawn_position worked"
	else:
		print("❌ clear_next_spawn_position failed")
		result_label.text += "\n❌ clear_next_spawn_position failed"

func test_teleport_position_mapping():
	print("\n🗺️ Testing TeleportPositionMapping:")
	result_label.text += "\n\nTesting TeleportPositionMapping..."
	
	if not TeleportPositionMapping:
		print("❌ TeleportPositionMapping not available!")
		result_label.text += "\n❌ TeleportPositionMapping not available!"
		return
	
	print("✅ TeleportPositionMapping available")
	result_label.text += "\n✅ TeleportPositionMapping available"
	
	# Test the route from lang_van_lang to dong_dau
	var test_route = "lang_van_lang_to_dong_dau"
	var position = TeleportPositionMapping.get_accurate_spawn_position("lang_van_lang", "dong_dau")
	
	if position != Vector2.ZERO:
		print("✅ Successfully retrieved position for %s: %s" % [test_route, position])
		result_label.text += "\n✅ Successfully retrieved position for %s: %s" % [test_route, position]
	else:
		print("❌ Failed to retrieve position for %s" % test_route)
		result_label.text += "\n❌ Failed to retrieve position for %s" % test_route
		
	# Test fallback to default position
	var fake_route = "fake_map_to_dong_dau"
	position = TeleportPositionMapping.get_accurate_spawn_position("fake_map", "dong_dau")
	
	if position != Vector2.ZERO:
		print("✅ Successfully used default position for fake route: %s" % position)
		result_label.text += "\n✅ Successfully used default position for fake route"
	else:
		print("❌ Failed to use default position for fake route")
		result_label.text += "\n❌ Failed to use default position for fake route"

func test_player_positioning():
	print("\n👤 Testing Player Positioning:")
	result_label.text += "\n\nTesting Player Positioning..."
	
	var player = get_tree().get_first_node_in_group("player")
	if not player:
		print("❌ Player not found!")
		result_label.text += "\n❌ Player not found!"
		return
	
	print("✅ Player found: %s" % player.name)
	result_label.text += "\n✅ Player found: %s" % player.name
	
	# Test moving player manually
	var original_position = player.global_position
	var test_position = Vector2(500, -300)
	
	print("Moving player from %s to %s" % [original_position, test_position])
	player.global_position = test_position
	
	if player.global_position.distance_to(test_position) < 10:
		print("✅ Successfully moved player to test position")
		result_label.text += "\n✅ Successfully moved player to test position"
	else:
		print("❌ Failed to move player to test position")
		result_label.text += "\n❌ Failed to move player to test position"
		
	# Move back to original position
	player.global_position = original_position
	print("Restored player to original position: %s" % original_position)
	
	# Test complete scenario with SceneManager
	if SceneManager:
		print("Testing complete teleport scenario...")
		result_label.text += "\n\nTesting complete teleport scenario..."
		
		# Step 1: Set a spawn position 
		var teleport_position = Vector2(400, -400)
		SceneManager.set_next_spawn_position(teleport_position)
		print("Set spawn position to %s" % teleport_position)
		
		# Step 2: Simulate player reading position from SceneManager
		if SceneManager.has_next_spawn_position():
			var target_pos = SceneManager.get_next_spawn_position()
			player.global_position = target_pos
			print("✅ Complete teleport scenario succeeded")
			result_label.text += "\n✅ Complete teleport scenario succeeded"
			
			# Restore player position
			player.global_position = original_position
			print("Restored player to original position: %s" % original_position)
		else:
			print("❌ Complete teleport scenario failed - position not set")
			result_label.text += "\n❌ Complete teleport scenario failed - position not set"
	else:
		print("❌ Cannot test complete scenario - SceneManager not available")
		result_label.text += "\n❌ Cannot test complete scenario - SceneManager not available"
