extends Node

func _ready():
	print("🔍 Debugging Teleport System")
	
	# <PERSON><PERSON><PERSON> tra cấu hình InputMap
	print("\n🎮 Input Mapping Check:")
	if InputMap.has_action("teleport_interact"):
		print("✅ teleport_interact action exists")
		var events = InputMap.action_get_events("teleport_interact")
		print("   Events count: " + str(events.size()))
		
		for event in events:
			if event is InputEventKey:
				print("   Key mapped: " + str(event.keycode) + " (Enter key is " + str(KEY_ENTER) + ")")
	else:
		print("❌ teleport_interact action does not exist")
		print("🔧 Creating teleport_interact action...")
		InputMap.add_action("teleport_interact")
		var event = InputEventKey.new()
		event.keycode = KEY_ENTER
		InputMap.action_add_event("teleport_interact", event)
		print("✅ Added Enter key to teleport_interact")
	
	# <PERSON><PERSON><PERSON> tra các cổng dịch chuyển
	print("\n🌀 Teleport Gates Check:")
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	if gates.size() > 0:
		print("✅ Found " + str(gates.size()) + " teleport gates")
		
		for gate in gates:
			print("\nGate: " + gate.name)
			print("   Gate ID: " + gate.gate_id)
			print("   Target Scene: " + gate.target_scene)
			print("   Auto Teleport: " + str(gate.auto_teleport))
			print("   Interaction Key: " + gate.interaction_key)
			print("   Monitoring: " + str(gate.monitoring))
			
			# Khắc phục vấn đề
			gate.auto_teleport = false  # Đảm bảo cổng dùng phím Enter
			gate.interaction_key = "teleport_interact"  # Đảm bảo dùng đúng action
			print("   ✅ Gate fixed: auto_teleport=false, interaction_key=teleport_interact")
	else:
		print("❌ No teleport gates found")

func _process(_delta):
	if Input.is_action_just_pressed("ui_accept"):
		print("\n🔍 Testing Enter key detection:")
		print("   ui_accept action triggered!")
		
	if Input.is_action_just_pressed("teleport_interact"):
		print("\n🔍 Testing Teleport Interact key detection:")
		print("   teleport_interact action triggered!")
		
	# Kiểm tra các cổng xem có player ở trong không
	if Input.is_key_pressed(KEY_F1):
		var gates = get_tree().get_nodes_in_group("teleport_gates")
		print("\n🔍 Checking gates player status:")
		for gate in gates:
			if gate.has_method("is_player_inside"):
				print("   Gate " + gate.name + ": player inside = " + str(gate.get_player_inside_state()))
			else:
				print("   Gate " + gate.name + ": _player_inside = " + str(gate._player_inside))
