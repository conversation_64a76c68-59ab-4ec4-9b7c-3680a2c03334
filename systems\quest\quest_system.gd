extends Node

signal quest_started(quest_id: String)
signal quest_updated(quest_id: String, progress: int, max_progress: int)
signal quest_completed(quest_id: String)

# Dictionary to store all active quests
# Format: { quest_id: { "progress": int, "max_progress": int, "completed": bool, "title": String, "description": String } }
var active_quests: Dictionary = {}

# Dictionary to store all completed quests
var completed_quests: Dictionary = {}

# Reference to the player
var player: Player = null

# Quest definitions
var quests: Dictionary = {
	"vua_hung_talk": {
		"title": "Trò chuyện với Vua Hùng",
		"description": "Tr<PERSON> chuyện với Vua Hùng về tương lai của làng.",
		"max_progress": 1,
		"reward": {
			"type": "equipment",
			"value": "starter_set"
		}
	},
	"defend_village": {
		"title": "Bảo vệ cổng làng",
		"description": "Kẻ thù sẽ tấn công từ 2 hướng (cổng trước và cổng sau), h<PERSON><PERSON> ti<PERSON><PERSON> diệt hết kẻ thù.",
		"max_progress": 50,
		"reward": {
			"type": "gold",
			"value": 100
		}
	}
}

func _ready() -> void:
	# Connect to necessary signals
	pass

func set_up_player(player_custom: Player) -> void:
	player = player_custom

# Start a new quest
func start_quest(quest_id: String) -> bool:
	if not quests.has(quest_id):
		print("Quest ID not found: " + quest_id)
		return false

	if active_quests.has(quest_id):
		print("Quest already active: " + quest_id)
		return false

	if completed_quests.has(quest_id):
		print("Quest already completed: " + quest_id)
		return false

	var quest_data = quests[quest_id]
	active_quests[quest_id] = {
		"progress": 0,
		"max_progress": quest_data.max_progress,
		"completed": false,
		"title": quest_data.title,
		"description": quest_data.description
	}

	print("Started quest: " + quest_data.title)
	emit_signal("quest_started", quest_id)
	return true

# Update quest progress
func update_quest_progress(quest_id: String, progress: int = 1) -> bool:
	if not active_quests.has(quest_id):
		print("Cannot update progress: Quest not active: " + quest_id)
		return false

	if active_quests[quest_id].completed:
		print("Cannot update progress: Quest already completed: " + quest_id)
		return false

	active_quests[quest_id].progress += progress

	# Cap progress at max_progress
	if active_quests[quest_id].progress > active_quests[quest_id].max_progress:
		active_quests[quest_id].progress = active_quests[quest_id].max_progress

	print("Updated quest progress: " + active_quests[quest_id].title + " - " +
		str(active_quests[quest_id].progress) + "/" + str(active_quests[quest_id].max_progress))

	emit_signal("quest_updated", quest_id, active_quests[quest_id].progress, active_quests[quest_id].max_progress)

	# Check if quest is completed
	if active_quests[quest_id].progress >= active_quests[quest_id].max_progress:
		complete_quest(quest_id)

	return true

# Complete a quest
func complete_quest(quest_id: String) -> bool:
	if not active_quests.has(quest_id):
		print("Cannot complete: Quest not active: " + quest_id)
		return false

	if active_quests[quest_id].completed:
		print("Quest already completed: " + quest_id)
		return false

	active_quests[quest_id].completed = true
	active_quests[quest_id].progress = active_quests[quest_id].max_progress

	# Move to completed quests
	completed_quests[quest_id] = active_quests[quest_id].duplicate()

	print("Completed quest: " + active_quests[quest_id].title)
	emit_signal("quest_completed", quest_id)

	# Give rewards
	give_quest_reward(quest_id)

	return true

# Give quest reward
func give_quest_reward(quest_id: String) -> void:
	if not quests.has(quest_id):
		print("Cannot give reward: Quest ID not found: " + quest_id)
		return

	var reward = quests[quest_id].reward

	match reward.type:
		"equipment":
			give_equipment_reward(reward.value)
		"gold":
			give_gold_reward(reward.value)
		_:
			print("Unknown reward type: " + reward.type)

# Give equipment reward
func give_equipment_reward(equipment_id: String) -> void:
	print("Giving equipment reward: " + equipment_id)

	# Lấy player từ scene hiện tại
	if not player:
		player = get_tree().get_first_node_in_group("player")
		if not player:
			print("ERROR: Player not found in scene")
			return

	match equipment_id:
		"starter_set":
			print("Giving starter equipment set to player")

			# Tăng chỉ số cơ bản cho người chơi
			player.max_health += 20
			player.health = player.max_health  # Hồi đầy máu
			player.max_mana += 10
			player.mana = player.max_mana  # Hồi đầy mana
			player.damage += 5

			# Hiển thị thông báo nhận trang bị
			_show_reward_notification("Đã nhận bộ trang bị khởi đầu!")
		_:
			print("Unknown equipment ID: " + equipment_id)

# Give gold reward
func give_gold_reward(amount: int) -> void:
	print("Giving gold reward: " + str(amount))

	# Lấy player từ scene hiện tại nếu chưa có
	if not player:
		player = get_tree().get_first_node_in_group("player")
		if not player:
			print("ERROR: Player not found in scene")
			return

	# Tăng XP cho người chơi thay vì vàng (vì không có hệ thống vàng)
	XPManager.add_xp(amount)

	# Hiển thị thông báo nhận vàng
	_show_reward_notification("Đã nhận " + str(amount) + " vàng!")

# Hiển thị thông báo phần thưởng
func _show_reward_notification(_message: String) -> void:
	# Tạo một label hiển thị thông báo
	var quest_notification = Label.new()
	quest_notification.text = _message
	
	# Thiết lập font và màu sắc
	var font_settings = LabelSettings.new()
	font_settings.font_size = 24
	font_settings.font_color = Color(1, 0.8, 0.2)  # Màu vàng
	font_settings.outline_size = 2
	font_settings.outline_color = Color(0, 0, 0)
	quest_notification.label_settings = font_settings

	# Thiết lập vị trí
	quest_notification.position = Vector2(640, 150)  # Giữa trên màn hình
	quest_notification.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	quest_notification.size = Vector2(600, 50)

	# Thêm vào scene
	var canvas_layer = CanvasLayer.new()
	canvas_layer.layer = 10  # Đảm bảo hiển thị trên cùng
	get_tree().root.add_child(canvas_layer)
	canvas_layer.add_child(quest_notification)

	# Animation hiển thị
	quest_notification.modulate.a = 0  # Bắt đầu trong suốt
	var tween = get_tree().create_tween()
	tween.tween_property(quest_notification, "modulate:a", 1.0, 0.5)  # Hiện dần
	tween.tween_interval(2.0)  # Hiển thị trong 2 giây
	tween.tween_property(quest_notification, "modulate:a", 0.0, 0.5)  # Mờ dần

	# Xóa sau khi hiển thị xong
	tween.tween_callback(func(): canvas_layer.queue_free())

# Check if a quest is active
func is_quest_active(quest_id: String) -> bool:
	return active_quests.has(quest_id) and not active_quests[quest_id].completed

# Check if a quest is completed
func is_quest_completed(quest_id: String) -> bool:
	return completed_quests.has(quest_id) or (active_quests.has(quest_id) and active_quests[quest_id].completed)

# Get quest progress
func get_quest_progress(quest_id: String) -> Dictionary:
	if active_quests.has(quest_id):
		return {
			"progress": active_quests[quest_id].progress,
			"max_progress": active_quests[quest_id].max_progress
		}
	elif completed_quests.has(quest_id):
		return {
			"progress": completed_quests[quest_id].max_progress,
			"max_progress": completed_quests[quest_id].max_progress
		}
	else:
		return {
			"progress": 0,
			"max_progress": 0
		}
