# Global UI Manager - <PERSON><PERSON><PERSON><PERSON> lý UI to<PERSON><PERSON> cụ<PERSON>
extends Node

var inventory_button_manager: CanvasLayer = null

func _ready() -> void:
	# Khởi tạo inventory button manager
	setup_inventory_button()

func setup_inventory_button() -> void:
	# Load inventory button manager scene
	var inventory_button_scene = load("res://ui/scenes/inventory_button_manager.tscn")
	if inventory_button_scene:
		inventory_button_manager = inventory_button_scene.instantiate()
		add_child(inventory_button_manager)
		print("Inventory button manager loaded")
	else:
		push_error("Cannot load inventory_button_manager.tscn")

# Hàm để toggle inventory từ các script khác
func toggle_inventory() -> void:
	if inventory_button_manager and inventory_button_manager.has_method("toggle_inventory"):
		inventory_button_manager.toggle_inventory()

# Hàm kiểm tra xem inventory có đang mở không
func is_inventory_open() -> bool:
	if inventory_button_manager and inventory_button_manager.has_method("is_inventory_tab_open"):
		return inventory_button_manager.is_inventory_tab_open()
	return false
