# test_lang_van_lang.gd - Script để test hệ thống <PERSON>
extends Node

func _ready():
	print("🧪 === TESTING LANG VAN LANG SYSTEM ===")
	call_deferred("run_tests")

func run_tests():
	await get_tree().process_frame
	
	print("1. Testing teleport gate scenes...")
	test_teleport_gate_scenes()
	
	print("2. Testing script references...")
	test_script_references()
	
	print("3. Testing input mapping...")
	test_input_mapping()
	
	print("✅ Testing completed!")

func test_teleport_gate_scenes():
	var scenes = [
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DongDau.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_HangAn.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_RungNuong.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DoiTre.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_SuoiThieng.tscn"
	]
	
	for scene_path in scenes:
		if FileAccess.file_exists(scene_path):
			print("   ✅ %s exists" % scene_path.get_file())
			
			# Try to load the scene
			var scene = load(scene_path)
			if scene:
				print("   ✅ %s loads successfully" % scene_path.get_file())
				
				# Try to instantiate
				var instance = scene.instantiate()
				if instance:
					print("   ✅ %s instantiates successfully" % scene_path.get_file())
					instance.queue_free()
				else:
					print("   ❌ %s failed to instantiate" % scene_path.get_file())
			else:
				print("   ❌ %s failed to load" % scene_path.get_file())
		else:
			print("   ❌ %s does not exist" % scene_path.get_file())

func test_script_references():
	var scripts = [
		"res://maps/lang_van_lang/scripts/lang_van_lang_map_controller.gd",
		"res://maps/lang_van_lang/scripts/lang_van_lang_teleport_system.gd",
		"res://maps/scripts/teleport_gate.gd"
	]
	
	for script_path in scripts:
		if FileAccess.file_exists(script_path):
			print("   ✅ %s exists" % script_path.get_file())
			
			# Try to load the script
			var script = load(script_path)
			if script:
				print("   ✅ %s loads successfully" % script_path.get_file())
			else:
				print("   ❌ %s failed to load" % script_path.get_file())
		else:
			print("   ❌ %s does not exist" % script_path.get_file())

func test_input_mapping():
	if InputMap.has_action("teleport_interact"):
		print("   ✅ teleport_interact action exists")
		
		var events = InputMap.action_get_events("teleport_interact")
		var has_m_key = false
		
		for event in events:
			if event is InputEventKey and event.keycode == KEY_M:
				has_m_key = true
				break
		
		if has_m_key:
			print("   ✅ M key is mapped to teleport_interact")
		else:
			print("   ⚠️ M key is not mapped to teleport_interact")
	else:
		print("   ⚠️ teleport_interact action does not exist")
