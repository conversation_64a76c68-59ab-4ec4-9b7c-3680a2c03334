
extends Node

signal scene_loaded

var _loading_screen = preload("res://ui/scenes/loading_screen.tscn")
var _current_scene = null
var _loading_screen_instance = null

# Player spawn management
var _next_spawn_position: Vector2 = Vector2.ZERO
var _has_spawn_position: bool = false

# Scene stack for MapScene toggle functionality
var _scene_stack: Array[String] = []
var _map_scene_path: String = "res://maps/New_loor/scenes/MapScene.tscn"
var _is_map_scene_open: bool = false

func _ready():
	var root = get_tree().root
	_current_scene = root.get_child(root.get_child_count() - 1)
	
	# If we're starting on a splash/menu scene, we don't need to show loading initially
	# If we're going straight to a gameplay scene, uncomment the next line:
	# call_deferred("goto_first_scene")

# Call this to load the first scene after splash/menu or boot scene
func goto_first_scene():
	# Use the lang_van_lang scene instead of main.tscn which doesn't exist
	goto_scene("res://maps/New_loor/scenes/main_foot.tscn")

# Call this function to change scenes with a loading screen
func goto_scene(path):
	print("🔄 SceneManager.goto_scene() được gọi với path: %s" % path)
	
	# Enhanced validation
	if path.is_empty():
		push_error("❌ Scene path is empty!")
		return
	
	if not ResourceLoader.exists(path):
		push_error("❌ Scene file does not exist: %s" % path)
		return
	
	# Test load scene first
	var test_resource = ResourceLoader.load(path)
	if not test_resource:
		push_error("❌ Cannot load scene resource: %s" % path)
		return
	
	# Show loading screen
	_loading_screen_instance = _loading_screen.instantiate()
	get_tree().root.add_child(_loading_screen_instance)
	print("📺 Loading screen đã được hiển thị")
	
	# Get the progress bar to show simulated progress
	var progress_bar = find_loading_progress_bar()
	if progress_bar:
		progress_bar.value = 0
		print("📊 Progress bar đã được thiết lập")
	else:
		print("⚠️ Không tìm thấy progress bar")
	
	# Wait a frame to let the loading screen appear
	await get_tree().process_frame
	
	# Simulate loading with a tween
	var tween = create_tween()
	if progress_bar:
		tween.tween_property(progress_bar, "value", 90, 1.0)
	
	# Wait for tween to finish
	await tween.finished
	
	# Try to load the scene
	var scene_resource
	
	# CRITICAL FIX: Ensure scene exists before loading
	if not ResourceLoader.exists(path):
		print("❌ Scene does not exist: %s" % path)
		if _loading_screen_instance:
			_loading_screen_instance.fade_out()
		return
	
	# Use standard load function
	scene_resource = load(path)
	if not scene_resource:
		print("❌ Lỗi loading scene: %s" % path)
		if _loading_screen_instance:
			_loading_screen_instance.fade_out()
		return
	
	print("✅ Scene đã được load thành công")
	
	# Change the scene
	var new_scene = scene_resource.instantiate()
	if not new_scene:
		print("❌ Lỗi instantiate scene: %s" % path)
		if _loading_screen_instance:
			_loading_screen_instance.fade_out()
		return
	
	# Set progress to 100%
	if progress_bar:
		progress_bar.value = 100
	
	# Wait a moment
	await get_tree().create_timer(0.3).timeout
	
	# Fade out loading screen
	if _loading_screen_instance != null and is_instance_valid(_loading_screen_instance):
		print("🎭 Đang fade out loading screen")
		_loading_screen_instance.fade_out()
		await get_tree().create_timer(0.5).timeout
	
	# Switch scenes
	var root = get_tree().root
	root.add_child(new_scene)
	root.remove_child(_current_scene)
	_current_scene.queue_free()
	_current_scene = new_scene
	
	print("🎬 Scene đã được chuyển đổi thành công")
	
	# 🎯 CRITICAL FIX: Setup player spawn position if available
	if _has_spawn_position:
		print("🎯 SceneManager: Setting up player spawn position")
		# Use call_deferred to ensure scene is fully loaded
		call_deferred("_setup_player_spawn")
	else:
		print("📍 SceneManager: No spawn position set")
	
	# Signal that scene is loaded
	scene_loaded.emit()

# Helper function to find the loading screen
func find_loading_screen():
	if _loading_screen_instance != null:
		return _loading_screen_instance
	return null

# Helper function to find the progress bar
func find_loading_progress_bar():
	var loading_screen = find_loading_screen()
	if loading_screen:
		return loading_screen.get_node_or_null("LoadingContainer/ProgressBar")
	return null

# Player spawn management methods
func set_next_spawn_position(position: Vector2) -> void:
	"""Thiết lập vị trí spawn cho scene tiếp theo"""
	_next_spawn_position = position
	_has_spawn_position = true
	print("📍 SceneManager: Set next spawn position: %s" % position)

func has_next_spawn_position() -> bool:
	"""Kiểm tra có vị trí spawn được thiết lập không"""
	return _has_spawn_position

func get_next_spawn_position() -> Vector2:
	"""Lấy vị trí spawn và clear nó"""
	if _has_spawn_position:
		var pos = _next_spawn_position
		_next_spawn_position = Vector2.ZERO
		_has_spawn_position = false
		return pos
	return Vector2.ZERO

func clear_next_spawn_position() -> void:
	"""Clear spawn position manually"""
	_next_spawn_position = Vector2.ZERO
	_has_spawn_position = false
	print("🗑️ SceneManager: Manually cleared spawn position")

func _setup_player_spawn() -> void:
	"""Thiết lập vị trí spawn cho player trong scene mới"""
	if not _has_spawn_position:
		print("⚠️ SceneManager: No spawn position set")
		return
	
	print("🎯 SceneManager: Setting up player spawn...")
	
	# Đợi một frame để scene hoàn tất loading
	await get_tree().process_frame
	
	# Tìm player trong scene
	var player = _find_player_in_scene()
	if player:
		print("🎯 SceneManager: Moving player to spawn position: %s" % _next_spawn_position)
		player.global_position = _next_spawn_position
		
		# Đảm bảo camera cập nhật
		var camera = get_viewport().get_camera_2d()
		if camera:
			camera.force_update_scroll()
		
		print("✅ SceneManager: Player moved to: %s" % player.global_position)
	else:
		print("⚠️ SceneManager: Player không tìm thấy trong scene mới")
		print("   This is normal if the scene uses Map Controllers for positioning")
	
	# Clear spawn position after use
	_next_spawn_position = Vector2.ZERO
	_has_spawn_position = false

func _find_player_in_scene() -> Node:
	"""Tìm player trong scene hiện tại"""
	var player = null
	
	# Method 1: Group
	var players = get_tree().get_nodes_in_group("player")
	if players.size() > 0:
		player = players[0]
	
	# Method 2: Direct search
	if not player:
		player = get_tree().current_scene.find_child("Player", true, false)
	
	# Method 3: Class-based search (if Player class exists)
	if not player:
		var all_nodes = get_tree().get_nodes_in_group("all")
		for node in all_nodes:
			if node.get_script() and "Player" in str(node.get_script()):
				player = node
				break
	
	return player 

# MapScene toggle functionality
func toggle_map_scene():
	"""Toggle MapScene - mở hoặc đóng tùy vào trạng thái hiện tại"""
	print("🔄 SceneManager.toggle_map_scene() called")
	print("🔍 Current state: _is_map_scene_open = %s" % _is_map_scene_open)
	print("🔍 Scene stack size: %d" % _scene_stack.size())
	
	if _is_map_scene_open:
		close_map_scene()
	else:
		open_map_scene()

func open_map_scene():
	"""Mở MapScene và lưu scene hiện tại vào stack"""
	print("📂 SceneManager.open_map_scene() called")
	
	if _is_map_scene_open:
		print("🗺️ MapScene đã mở rồi")
		return
	
	# Kiểm tra _current_scene tồn tại
	if not _current_scene:
		print("⚠️ Current scene không tồn tại")
		return
	
	# Lưu scene hiện tại vào stack
	var current_scene_path = ""
	if _current_scene and is_instance_valid(_current_scene):
		if _current_scene.scene_file_path:
			current_scene_path = _current_scene.scene_file_path
		else:
			print("⚠️ Scene file path is null or empty")
	else:
		print("⚠️ _current_scene is null or invalid")
		return
	
	if current_scene_path != "":
		_scene_stack.push_back(current_scene_path)
		print("📚 Đã lưu scene hiện tại vào stack: %s" % current_scene_path)
	else:
		print("⚠️ Scene hiện tại không có file path, sẽ ghi nhớ bằng tên scene")
		# Nếu không có đường dẫn file, dùng tên scene để backup
		if _current_scene and _current_scene.name:
			_scene_stack.push_back("SCENE:" + _current_scene.name)
			print("📚 Đã lưu tên scene vào stack: %s" % _current_scene.name)
	
	# Mở MapScene
	_is_map_scene_open = true
	print("🔄 Setting _is_map_scene_open = true")
	goto_scene_without_loading(_map_scene_path)
	print("🗺️ Đã mở MapScene")

func close_map_scene():
	"""Đóng MapScene và quay về scene trước đó"""
	print("📁 SceneManager.close_map_scene() called")
	
	if not _is_map_scene_open:
		print("🗺️ MapScene chưa mở")
		return
	
	# Kiểm tra có scene trong stack không
	if _scene_stack.is_empty():
		print("⚠️ Không có scene trong stack để quay về")
		return
	
	# Lấy scene cuối cùng từ stack
	var previous_scene_path = _scene_stack.pop_back()
	_is_map_scene_open = false
	print("🔄 Setting _is_map_scene_open = false")
	
	# Kiểm tra nếu đó là một backup bằng tên scene
	if previous_scene_path.begins_with("SCENE:"):
		print("⚠️ Không thể quay về scene chỉ có tên không có path: %s" % previous_scene_path)
		print("⚠️ Mở scene mặc định thay thế")
		goto_scene("res://maps/New_loor/scenes/main_foot.tscn")
		return
		
	goto_scene_without_loading(previous_scene_path)
	print("↩️ Đã quay về scene: %s" % previous_scene_path)

func goto_scene_without_loading(path: String):
	"""Chuyển scene mà không hiển thị loading screen (dành cho toggle nhanh)"""
	print("🔄 SceneManager.goto_scene_without_loading() được gọi với path: %s" % path)
	
	# Try to load the scene
	var scene_resource = load(path)
	if not scene_resource:
		print("❌ Lỗi loading scene: %s" % path)
		return
	
	print("✅ Scene đã được load thành công")
	
	# Change the scene immediately
	var new_scene = scene_resource.instantiate()
	
	# Switch scenes
	var root = get_tree().root
	root.add_child(new_scene)
	
	# Safely remove old scene
	if _current_scene and is_instance_valid(_current_scene):
		root.remove_child(_current_scene)
		_current_scene.queue_free()
	
	_current_scene = new_scene
	
	print("🎬 Scene đã được chuyển đổi thành công (không loading screen)")
	
	# Safe access to scene properties
	if new_scene and is_instance_valid(new_scene):
		print("🔍 New scene: %s" % new_scene.name)
		var scene_path = new_scene.scene_file_path if new_scene.scene_file_path else "No path"
		print("🔍 Scene file path: %s" % scene_path)
	else:
		print("⚠️ New scene is invalid")
	
	# Wait a frame for scene to be fully ready
	await get_tree().process_frame
	
	# Signal that scene is loaded
	scene_loaded.emit()

func is_map_scene_open() -> bool:
	"""Kiểm tra MapScene có đang mở không"""
	return _is_map_scene_open
