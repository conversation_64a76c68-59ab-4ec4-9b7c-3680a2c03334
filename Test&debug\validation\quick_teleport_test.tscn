[gd_scene load_steps=2 format=3 uid="uid://dc4txv8h1uu1n"]

[ext_resource type="Script" path="res://Test&debug/validation/quick_teleport_test.gd" id="1_abc12"]

[node name="QuickTeleportTest" type="Node"]
script = ExtResource("1_abc12")

[node name="Label" type="Label" parent="."]
offset_left = 10.0
offset_top = 10.0
offset_right = 600.0
offset_bottom = 100.0
text = "🧪 QUICK TELEPORT CONTEXT TEST

Press Enter để chạy test TeleportContextManager
Xem Console để theo dõi kết quả

Note: Cần restart Godot để TeleportContextManager autoload có hiệu lực"
