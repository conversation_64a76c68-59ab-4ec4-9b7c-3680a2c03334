# Test Script cho UI và Teleport System
# Ch<PERSON><PERSON> trong Godot Editor Console hoặc Debug

extends Node

func _ready():
	test_ui_and_teleport_system()

func test_ui_and_teleport_system():
	print("🧪 === TESTING UI & TELEPORT SYSTEM ===")
	
	# Test Map Name Display
	print("📍 Testing Map Name Display...")
	var ui_scene = preload("res://ui/scenes/map_name_display.tscn").instantiate()
	get_tree().root.add_child(ui_scene)
	ui_scene.set_map_name("Test Map Name")
	
	# Test Teleport Gates Input
	print("⌨️ Testing Teleport Input Mapping...")
	if InputMap.has_action("teleport_interact"):
		print("✅ teleport_interact action exists")
		var events = InputMap.action_get_events("teleport_interact")
		for event in events:
			if event is InputEventKey:
				print("🔑 Key mapped: " + str(event.keycode))
				if event.keycode == KEY_ENTER:
					print("✅ Enter key correctly mapped")
				elif event.keycode == KEY_M:
					print("⚠️ WARNING: M key still mapped!")
	else:
		print("❌ teleport_interact action not found")
	
	print("🧪 === TEST COMPLETED ===")
