extends Control

@onready var tab_container: HBoxContainer = $CanvasLayer/All_Tab_Button
@onready var nhiem_vu_button: TextureButton = $CanvasLayer/All_Tab_Button/Mission_Button
@onready var hanh_trang_button: TextureButton = $CanvasLayer/All_Tab_Button/Inventory_Button
@onready var ky_nang_button: TextureButton = $CanvasLayer/All_Tab_Button/Skills_Button
@onready var khac_button: TextureButton = $CanvasLayer/All_Tab_Button/Etc_Button
@onready var close_button: TextureButton = $CanvasLayer/Close_Button

# Quest display variables
var quest_container: VBoxContainer
var quest_label_template: Label

func _ready() -> void:
	# Kiểm tra xem các node có được tìm thấy không
	if not tab_container:
		push_error("HBoxContainer not found at path: CanvasLayer/All_Tab_Button")
		return
	if not nhiem_vu_button:
		push_error("Mission_Button not found at path: CanvasLayer/All_Tab_Button/Mission_Button")
		return
	if not hanh_trang_button:
		push_error("Inventory_Button not found at path: CanvasLayer/All_Tab_Button/Inventory_Button")
		return
	if not ky_nang_button:
		push_error("Skills_Button not found at path: CanvasLayer/All_Tab_Button/Skills_Button")
		return
	if not khac_button:
		push_error("Etc_Button not found at path: CanvasLayer/All_Tab_Button/Etc_Button")
		return
	if not close_button:
		push_error("Close_Button not found at path: CanvasLayer/Close_Button")
		return

	# Kết nối tín hiệu pressed của từng nút
	nhiem_vu_button.connect("pressed", _on_nhiem_vu_button_pressed)
	hanh_trang_button.connect("pressed", _on_hanh_trang_button_pressed)
	ky_nang_button.connect("pressed", _on_ky_nang_button_pressed)
	khac_button.connect("pressed", _on_khac_button_pressed)
	close_button.connect("pressed", _on_close_button_pressed)

	# Create quest container
	setup_quest_display()

	# Connect to quest signals
	QuestSystem.quest_started.connect(_on_quest_started)
	QuestSystem.quest_updated.connect(_on_quest_updated)
	QuestSystem.quest_completed.connect(_on_quest_completed)

	# Display current quests
	update_quest_display()

func _on_nhiem_vu_button_pressed() -> void:

	pass

func _on_hanh_trang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/inventory_tab.tscn")

func _on_ky_nang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/skills_tab.tscn")

func _on_khac_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/etc_tab.tscn")

func _on_close_button_pressed() -> void:
	queue_free()  # Xóa node Missions_Tab khỏi Scene Tree

# Quest display functions
func setup_quest_display() -> void:
	# Create a container for quests
	quest_container = VBoxContainer.new()
	quest_container.name = "QuestContainer"
	quest_container.position = Vector2(50, 200)
	quest_container.size = Vector2(380, 400)
	quest_container.custom_minimum_size = Vector2(380, 400)
	$CanvasLayer.add_child(quest_container)

	# Create a template label for quests
	quest_label_template = Label.new()
	quest_label_template.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	quest_label_template.horizontal_alignment = HORIZONTAL_ALIGNMENT_LEFT
	quest_label_template.vertical_alignment = VERTICAL_ALIGNMENT_TOP

	# Set font size and color
	var font_settings = LabelSettings.new()
	font_settings.font_size = 18
	font_settings.font_color = Color(1, 1, 1)
	font_settings.outline_size = 2
	font_settings.outline_color = Color(0, 0, 0)
	quest_label_template.label_settings = font_settings

func update_quest_display() -> void:
	# Clear existing quest display
	if quest_container and is_instance_valid(quest_container):
		for child in quest_container.get_children():
			child.queue_free()
	else:
		print("WARNING: quest_container is null or invalid")
		return

	# Add a title
	var title_label = Label.new()
	title_label.text = "Nhiệm vụ hiện tại:"
	var title_font = LabelSettings.new()
	title_font.font_size = 24
	title_font.font_color = Color(1, 0.8, 0.2)
	title_font.outline_size = 2
	title_font.outline_color = Color(0, 0, 0)
	title_label.label_settings = title_font
	quest_container.add_child(title_label)

	# Add a spacer
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 20)
	quest_container.add_child(spacer)

	# Display active quests
	var has_quests = false

	for quest_id in QuestSystem.active_quests:
		var quest_data = QuestSystem.active_quests[quest_id]

		var quest_label = quest_label_template.duplicate()
		var progress_text = ""

		if quest_data.max_progress > 1:
			progress_text = " (" + str(quest_data.progress) + "/" + str(quest_data.max_progress) + ")"

		quest_label.text = quest_data.title + progress_text + "\n" + quest_data.description

		if quest_data.completed:
			quest_label.modulate = Color(0.5, 1, 0.5)  # Green for completed

		quest_container.add_child(quest_label)

		# Add a small spacer between quests
		var quest_spacer = Control.new()
		quest_spacer.custom_minimum_size = Vector2(0, 10)
		quest_container.add_child(quest_spacer)

		has_quests = true

	# If no active quests, show a message
	if not has_quests:
		var no_quests_label = quest_label_template.duplicate()
		no_quests_label.text = "Không có nhiệm vụ nào đang hoạt động."
		quest_container.add_child(no_quests_label)

# Quest signal handlers
func _on_quest_started(_quest_id: String) -> void:
	update_quest_display()

func _on_quest_updated(_quest_id: String, _progress: int, _max_progress: int) -> void:
	update_quest_display()

func _on_quest_completed(_quest_id: String) -> void:
	update_quest_display()
