extends EditorPlugin

# Plugin để tự động fix một số lỗi phổ biến trong game

func _enter_tree():
	# Plugin được kích hoạt
	print("🔧 Fix Plugin activated")

func _exit_tree():
	# Plugin bị vô hiệu hóa
	print("🔧 Fix Plugin deactivated")

# <PERSON>à<PERSON> chạy mỗi frame trong editor để khắc phục vấn đề
func _process(_delta):
	if Engine.is_editor_hint():
		# Chỉ chạy trong editor
		return
		
	# Khắc phục vấn đề GlobalMapNameUI nếu cần
	if GlobalMapNameUI and not GlobalMapNameUI.map_name_ui:
		print("🛠️ Fix Plugin: Creating missing GlobalMapNameUI...")
		GlobalMapNameUI._create_persistent_map_ui()
		
	if GlobalMapNameUI and GlobalMapNameUI.map_name_label and not GlobalMapNameUI.map_name_label.visible:
		GlobalMapNameUI.map_name_label.visible = true
		print("🛠️ Fix Plugin: Fixed map name visibility")
		
	# <PERSON>h<PERSON>c phục vấn đề teleport input
	if not InputMap.has_action("teleport_interact"):
		InputMap.add_action("teleport_interact")
		var event = InputEventKey.new()
		event.keycode = KEY_ENTER
		InputMap.action_add_event("teleport_interact", event)
		print("🛠️ Fix Plugin: Added teleport_interact input mapping")
