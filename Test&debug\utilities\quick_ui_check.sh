#!/bin/bash
echo "🔍 Quick UI System Check"
echo "========================="

echo "1. Checking autoload configuration..."
grep -n "GlobalMapNameUI" project.godot

echo ""
echo "2. Checking if GlobalMapNameUI script exists..."
if [ -f "ui/scripts/global_map_name_ui.gd" ]; then
    echo "✅ GlobalMapNameUI script found"
else
    echo "❌ GlobalMapNameUI script missing"
fi

echo ""
echo "3. Checking map controllers..."
echo "Controllers using GlobalMapNameUI:"
grep -l "GlobalMapNameUI.set_map_name" maps/*/scripts/*_map_controller.gd | wc -l
echo "Total controllers found"

echo ""
echo "4. Checking for old UI system usage..."
OLD_USAGE=$(grep -l "map_name_display.tscn" maps/*/scripts/*.gd 2>/dev/null | wc -l)
echo "Controllers using old UI system: $OLD_USAGE"

echo ""
echo "========================="
echo "✅ Quick check completed!"
echo "To test UI:"
echo "1. Open debug_ui_test.tscn in Godot"
echo "2. Run scene (F6)"
echo "3. Check console for debug output"
echo "========================="
