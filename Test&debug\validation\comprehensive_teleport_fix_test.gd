# comprehensive_teleport_fix_test.gd - Test toàn diện gi<PERSON>i ph<PERSON>p teleport
extends Node2D

var test_phase: String = ""
var results: Array = []

func _ready():
	print("🎯 Comprehensive Teleport Fix Test")
	print("This will test the complete teleport position fix solution")
	call_deferred("run_comprehensive_test")

func run_comprehensive_test():
	print("\n🧪 === COMPREHENSIVE TELEPORT FIX TEST ===")
	
	# Phase 1: System availability
	test_system_availability()
	
	# Phase 2: Context management
	test_context_management()
	
	# Phase 3: Position mapping integration
	test_position_mapping_integration()
	
	# Phase 4: Simulate real teleport scenario
	test_real_teleport_scenario()
	
	# Results
	display_results()

func test_system_availability():
	test_phase = "System Availability"
	print("\n📋 Phase 1: Testing System Availability")
	
	# Check all required autoloads
	var systems = {
		"SceneManager": SceneManager,
		"TeleportPositionMapping": TeleportPositionMapping,
		"TeleportContextManager": get_node_or_null("/root/TeleportContextManager")
	}
	
	for system_name in systems:
		var system = systems[system_name]
		if system:
			add_result("✅", "%s available" % system_name)
		else:
			add_result("❌", "%s missing" % system_name)

func test_context_management():
	test_phase = "Context Management"
	print("\n📋 Phase 2: Testing Context Management")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		add_result("❌", "TeleportContextManager not available - skip context tests")
		return
	
	# Test context creation
	context_manager.set_teleport_context(
		"lang_van_lang_to_dong_dau",
		"lang_van_lang",
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	)
	
	if context_manager.validate_context():
		add_result("✅", "Context creation successful")
		
		var summary = context_manager.get_context_summary()
		add_result("ℹ️", "Context: %s" % summary)
		
		if context_manager.has_valid_position():
			add_result("✅", "Valid spawn position determined")
		else:
			add_result("❌", "No valid spawn position")
	else:
		add_result("❌", "Context creation failed")
	
	# Clean up
	context_manager.force_clear_context()

func test_position_mapping_integration():
	test_phase = "Position Mapping Integration"
	print("\n📋 Phase 3: Testing Position Mapping Integration")
	
	if not TeleportPositionMapping:
		add_result("❌", "TeleportPositionMapping not available")
		return
	
	# Test critical routes
	var test_routes = [
		["lang_van_lang", "dong_dau", Vector2(-1421, -429)],
		["dong_dau", "lang_van_lang", Vector2(300, -1900)],
		["lang_van_lang", "rung_nuong", Vector2(753, -1225)]
	]
	
	for route in test_routes:
		var from_map = route[0]
		var to_map = route[1] 
		var expected_pos = route[2]
		
		var actual_pos = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		
		if actual_pos == expected_pos:
			add_result("✅", "Route %s -> %s: %s" % [from_map, to_map, actual_pos])
		else:
			add_result("❌", "Route %s -> %s: expected %s, got %s" % [from_map, to_map, expected_pos, actual_pos])

func test_real_teleport_scenario():
	test_phase = "Real Teleport Scenario"
	print("\n📋 Phase 4: Testing Real Teleport Scenario")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		add_result("❌", "Cannot test real scenario - TeleportContextManager missing")
		return
	
	# Simulate the exact problem scenario from the bug report
	# Player ở Lang Van Lang muốn teleport đến Dong Dau
	var test_scenarios = [
		{
			"name": "Lang Van Lang -> Dong Dau",
			"gate_id": "lang_van_lang_to_dong_dau", 
			"from_map": "lang_van_lang",
			"target_scene": "res://maps/dong_dau/scenes/dong_dau.tscn",
			"expected_position": Vector2(-1421, -429)
		},
		{
			"name": "Dong Dau -> Lang Van Lang",
			"gate_id": "dong_dau_to_lang_van_lang",
			"from_map": "dong_dau", 
			"target_scene": "res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
			"expected_position": Vector2(300, -1900)
		}
	]
	
	for scenario in test_scenarios:
		print("\n🎯 Testing scenario: %s" % scenario.name)
		
		# Create context như TeleportGate sẽ làm
		context_manager.set_teleport_context(
			scenario.gate_id,
			scenario.from_map,
			scenario.target_scene
		)
		
		if context_manager.validate_context():
			var spawn_pos = context_manager.get_spawn_position_for_current_context()
			
			if spawn_pos == scenario.expected_position:
				add_result("✅", "Scenario %s: Correct position %s" % [scenario.name, spawn_pos])
			else:
				add_result("❌", "Scenario %s: Expected %s, got %s" % [scenario.name, scenario.expected_position, spawn_pos])
		else:
			add_result("❌", "Scenario %s: Context validation failed" % scenario.name)

func add_result(icon: String, message: String):
	results.append({"icon": icon, "message": message, "phase": test_phase})
	print("   %s %s" % [icon, message])

func display_results():
	print("\n📊 === TEST RESULTS SUMMARY ===")
	
	var pass_count = 0
	var fail_count = 0
	var info_count = 0
	
	for result in results:
		match result.icon:
			"✅": pass_count += 1
			"❌": fail_count += 1  
			"ℹ️", "⚠️": info_count += 1
	
	print("✅ PASS: %d" % pass_count)
	print("❌ FAIL: %d" % fail_count)
	print("ℹ️ INFO: %d" % info_count)
	
	if fail_count == 0:
		print("\n🎉 ALL TESTS PASSED! Teleport fix should work correctly.")
		print("🎯 Ready for real-world testing!")
	else:
		print("\n⚠️ ISSUES FOUND. Please fix these before testing:")
		for result in results:
			if result.icon == "❌":
				print("   • %s" % result.message)
	
	print("\n📋 Next Steps:")
	print("1. If tests pass: Test trong game thực tế")
	print("2. If tests fail: Fix issues và chạy lại test")
	print("3. Monitor console logs khi teleport để verify behavior")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter
		run_comprehensive_test()
	elif event.is_action_pressed("ui_select"):  # Space  
		debug_current_state()

func debug_current_state():
	print("\n🔍 === CURRENT STATE DEBUG ===")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if context_manager:
		if context_manager.has_active_teleport_context():
			print("📋 Active context: %s" % context_manager.get_context_summary())
		else:
			print("📋 No active teleport context")
	else:
		print("❌ TeleportContextManager not available")
	
	if SceneManager and SceneManager.has_next_spawn_position():
		print("📍 SceneManager spawn position: %s" % SceneManager.get_next_spawn_position())
	else:
		print("📍 No SceneManager spawn position")
	
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("👤 Current player position: %s" % player.global_position)
	else:
		print("👤 No player found")
