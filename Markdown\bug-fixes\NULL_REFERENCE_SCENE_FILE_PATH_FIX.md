# Null Reference Fix Report - scene_file_path Error

## 🐛 Lỗi: `Invalid access to property or key 'scene_file_path' on a base object of type 'null instance'`

### 🔍 Nguyên nhân:
Lỗi x<PERSON>y ra khi:
1. `_current_scene` bị null sau `queue_free()`
2. `current_scene` từ `get_tree().current_scene` trả về null
3. Scene objects không valid sau scene transitions

### ✅ Fixes Applied:

#### 1. SceneManager.gd - Enhanced null safety

**open_map_scene():**
```gdscript
# TRƯỚC (có thể lỗi)
var current_scene_path = _current_scene.scene_file_path

# SAU (null safe)
var current_scene_path = ""
if _current_scene and is_instance_valid(_current_scene):
    current_scene_path = _current_scene.scene_file_path
else:
    print("⚠️ _current_scene is null or invalid")
    return
```

**goto_scene_without_loading():**
```gdscript
# TRƯỚC (có thể lỗi)
root.remove_child(_current_scene)
_current_scene.queue_free()
print("🔍 Scene file path: %s" % new_scene.scene_file_path)

# SAU (null safe)
# Safely remove old scene
if _current_scene and is_instance_valid(_current_scene):
    root.remove_child(_current_scene)
    _current_scene.queue_free()

# Safe access to scene properties
if new_scene and is_instance_valid(new_scene):
    var scene_path = new_scene.scene_file_path if new_scene.scene_file_path else "No path"
    print("🔍 Scene file path: %s" % scene_path)
```

#### 2. MapToggleManager.gd - Current scene validation

```gdscript
# TRƯỚC (có thể lỗi)
var current_scene = get_tree().current_scene
if not current_scene:
    return
var current_scene_path = current_scene.scene_file_path

# SAU (null safe)
var current_scene = get_tree().current_scene
if not current_scene or not is_instance_valid(current_scene):
    print("⚠️ Current scene không tồn tại hoặc không hợp lệ")
    return

var current_scene_path = ""
if current_scene.scene_file_path:
    current_scene_path = current_scene.scene_file_path
```

## 🔒 Safety Patterns Added:

1. **Null checks**: `if object and is_instance_valid(object)`
2. **Property access**: Check property exists before access
3. **Early returns**: Exit functions when invalid state detected
4. **Default values**: Use empty strings instead of null access
5. **Validation logging**: Clear error messages for debugging

## 🧪 Test Instructions:

1. **Mở Godot editor**
2. **Chạy test scene**
3. **Toggle MapScene nhiều lần**
4. **Check console** - Should not see null reference errors
5. **Monitor behavior** - Toggle should work smoothly

### Expected Console Output (No Errors):
```
🔍 MapToggleManager: Detected M key press
🔍 Current scene: TestMapToggle
🔍 Current scene path: 'res://maps/test_map_toggle.tscn'
📂 SceneManager.open_map_scene() called
📚 Đã lưu scene hiện tại vào stack: res://maps/test_map_toggle.tscn
🔍 New scene: MapScene
🔍 Scene file path: res://maps/New_loor/scenes/MapScene.tscn
```

## 🚀 Prevention Measures:

- **Consistent validation** across all scene operations
- **is_instance_valid()** checks before property access
- **Safe property access** with fallbacks
- **Enhanced error logging** for easier debugging

---
**Status**: ✅ NULL REFERENCE ERRORS FIXED  
**Files Modified**: 2 core files  
**Impact**: Stable scene switching without crashes
