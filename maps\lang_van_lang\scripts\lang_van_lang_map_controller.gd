# LangVanLangMapController.gd - Controller cho map Làng Văn Lang
extends Node2D
class_name LangVanLangMapController

# ----- Signals -----
signal map_loaded
signal teleport_gate_activated(gate_id: String)

# ----- Node References -----
@onready var player: Player = null
@onready var teleport_system: LangVanLangTeleportSystem = null

# ----- Map Settings -----
var map_id: String = "lang_van_lang"
var map_name: String = "Làng Văn Lang"

func _ready() -> void:
	# Add to map controllers group for debugging
	add_to_group("map_controllers")
	
	print("🏛️ Lang Van Lang Map Controller initialized")
	call_deferred("_setup_map")

func _setup_map() -> void:
	# Tìm player trong scene
	_find_player()

	# Check if player needs to be repositioned (for teleport)
	_check_and_setup_teleport_spawn()

	# Thiết lập hệ thống dịch chuyển
	_setup_teleport_system()

	# Hi<PERSON><PERSON> thị tên bản đồ
	_show_map_name_ui()

	# Emit signal map đã load xong
	map_loaded.emit()
	print("✅ Lang Van Lang map setup completed")

func _show_map_name_ui() -> void:
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name(map_name)
		print("🗺️ Updated global map name UI: %s" % map_name)
	else:
		print("⚠️ GlobalMapNameUI not available")

func _find_player() -> void:
	# Tìm player node
	player = get_tree().get_first_node_in_group("player")
	if not player:
		# Thử tìm theo đường dẫn
		player = get_node_or_null("Player")
	
	if player:
		print("👤 Player found in Lang Van Lang map: %s" % player.name)
		print("Player position: ", player.global_position)
		
		# Auto-fix player position if came from teleport
		_auto_fix_teleport_position()
	else:
		print("⚠️ WARNING: Player not found in Lang Van Lang map")

func _check_and_setup_teleport_spawn() -> void:
	# Check if player needs to be repositioned (for teleport)
	_auto_fix_teleport_position()

func _auto_fix_teleport_position() -> void:
	"""Tự động sửa vị trí player nếu đến từ teleport"""
	if not player:
		return
	
	# Check if SceneManager has spawn position set
	if SceneManager and SceneManager.has_next_spawn_position():
		var target_pos = SceneManager.get_next_spawn_position()
		print("🎯 Auto-fixing player position from %s to %s" % [player.global_position, target_pos])
		
		# Set player position
		player.global_position = target_pos
		
		print("✅ Player repositioned successfully to: %s" % player.global_position)
	else:
		print("📍 No teleport spawn position set, keeping default position")

func _setup_teleport_system() -> void:
	# Tạo hệ thống teleport mới
	teleport_system = preload("res://maps/lang_van_lang/scripts/lang_van_lang_teleport_system.gd").new()
	teleport_system.name = "TeleportSystem"
	add_child(teleport_system)
	
	# Kết nối signals
	if not teleport_system.gate_activated.is_connected(_on_teleport_gate_activated):
		teleport_system.gate_activated.connect(_on_teleport_gate_activated)
	
	print("🌀 Teleport system integrated successfully")

func _on_teleport_gate_activated(gate_id: String) -> void:
	print("🚪 Map Controller: Teleport gate activated - %s" % gate_id)
	teleport_gate_activated.emit(gate_id)

# Public Methods
func get_teleport_system() -> LangVanLangTeleportSystem:
	return teleport_system

func enable_teleport_gate(gate_id: String) -> void:
	if teleport_system:
		teleport_system.enable_gate(gate_id)

func disable_teleport_gate(gate_id: String) -> void:
	if teleport_system:
		teleport_system.disable_gate(gate_id)

func get_all_teleport_gates() -> Array:
	if teleport_system:
		return teleport_system.get_all_gates()
	return []

# Debug Methods
func debug_teleport_system() -> void:
	if teleport_system:
		teleport_system.debug_info()
	else:
		print("⚠️ No teleport system available")
