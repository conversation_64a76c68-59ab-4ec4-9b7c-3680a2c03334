# Teleport Position Mapping System - Quản lý vị trí spawn ch<PERSON><PERSON> xác cho teleport
extends Node

# Ma trận mapping vị trí spawn cho mỗi teleport gate
var position_mappings: Dictionary = {
	# Lang Van Lang -> Other Maps (cổng lang_van_lang sẽ spawn ở vị trí tương ứng trong map đích)
	"lang_van_lang_to_rung_nuong": Vector2(753, -1225),
	"lang_van_lang_to_dong_dau": Vector2(-1421, -429),
	"lang_van_lang_to_hang_an": Vector2(-2069, 484),
	"lang_van_lang_to_suoi_thieng": Vector2(-2069, 484),
	"lang_van_lang_to_doi_tre": Vector2(-2292, -538),
	
	# Rung Nuong -> Other Maps (cổng rung_nuong sẽ spawn ở vị trí tương ứng trong map đích)
	"rung_nuong_to_lang_van_lang": Vector2(300, -1900),  # Vị trí cổng lang_van_lang trong lang_van_lang map
	"rung_nuong_to_dong_dau": Vector2(-1421, -429),    # Vị trí cổng rung_nuong trong dong_dau map
	"rung_nuong_to_hang_an": Vector2(-2069, 484),      # Vị trí cổng rung_nuong trong hang_an map
	"rung_nuong_to_suoi_thieng": Vector2(-2069, 484),  # Vị trí cổng rung_nuong trong suoi_thieng map
	"rung_nuong_to_doi_tre": Vector2(-2292, -538),     # Vị trí cổng rung_nuong trong doi_tre map
	
	# Dong Dau -> Other Maps (cổng dong_dau sẽ spawn ở vị trí tương ứng trong map đích)
	"dong_dau_to_lang_van_lang": Vector2(300, -1900),   # Vị trí cổng dong_dau trong lang_van_lang map
	"dong_dau_to_rung_nuong": Vector2(753, -1225),      # Vị trí cổng dong_dau trong rung_nuong map
	"dong_dau_to_doi_tre": Vector2(-2292, -538),        # Vị trí cổng dong_dau trong doi_tre map
	"dong_dau_to_hang_an": Vector2(-2069, 484),         # Vị trí cổng dong_dau trong hang_an map
	"dong_dau_to_suoi_thieng": Vector2(-2069, 484),     # Vị trí cổng dong_dau trong suoi_thieng map
	
	# Hang An -> Other Maps (cổng hang_an sẽ spawn ở vị trí tương ứng trong map đích)
	"hang_an_to_lang_van_lang": Vector2(300, -1900),    # Vị trí cổng hang_an trong lang_van_lang map
	"hang_an_to_rung_nuong": Vector2(753, -1225),       # Vị trí cổng hang_an trong rung_nuong map
	"hang_an_to_dong_dau": Vector2(-1421, -429),        # Vị trí cổng hang_an trong dong_dau map
	"hang_an_to_suoi_thieng": Vector2(-2069, 484),      # Vị trí cổng hang_an trong suoi_thieng map
	"hang_an_to_doi_tre": Vector2(-2292, -538),         # Vị trí cổng hang_an trong doi_tre map
	
	# Suoi Thieng -> Other Maps (cổng suoi_thieng sẽ spawn ở vị trí tương ứng trong map đích)
	"suoi_thieng_to_lang_van_lang": Vector2(300, -1900), # Vị trí cổng suoi_thieng trong lang_van_lang map
	"suoi_thieng_to_rung_nuong": Vector2(753, -1225),    # Vị trí cổng suoi_thieng trong rung_nuong map
	"suoi_thieng_to_dong_dau": Vector2(-1421, -429),     # Vị trí cổng suoi_thieng trong dong_dau map
	"suoi_thieng_to_hang_an": Vector2(-2069, 484),       # Vị trí cổng suoi_thieng trong hang_an map
	"suoi_thieng_to_doi_tre": Vector2(-2292, -538),      # Vị trí cổng suoi_thieng trong doi_tre map
	
	# Doi Tre -> Other Maps (cổng doi_tre sẽ spawn ở vị trí tương ứng trong map đích)
	"doi_tre_to_lang_van_lang": Vector2(300, -1900),     # Vị trí cổng doi_tre trong lang_van_lang map
	"doi_tre_to_rung_nuong": Vector2(753, -1225),        # Vị trí cổng doi_tre trong rung_nuong map
	"doi_tre_to_dong_dau": Vector2(-1421, -429),         # Vị trí cổng doi_tre trong dong_dau map
	"doi_tre_to_hang_an": Vector2(-2069, 484),           # Vị trí cổng doi_tre trong hang_an map
	"doi_tre_to_suoi_thieng": Vector2(-2069, 484)        # Vị trí cổng doi_tre trong suoi_thieng map
}

func _ready():
	print("🎯 TeleportPositionMapping system initialized")
	print("📊 Total position mappings: %d" % position_mappings.size())
	_validate_all_mappings()

func _validate_all_mappings():
	"""Kiểm tra tất cả mappings để đảm bảo tính đối xứng"""
	print("\n🔍 Validating teleport position mappings...")
	var maps = ["lang_van_lang", "rung_nuong", "dong_dau", "hang_an", "suoi_thieng", "doi_tre"]
	var missing_mappings = []
	
	for from_map in maps:
		for to_map in maps:
			if from_map != to_map:
				var mapping_key = from_map + "_to_" + to_map
				if not position_mappings.has(mapping_key):
					missing_mappings.append(mapping_key)
	
	if missing_mappings.size() > 0:
		print("⚠️ Missing mappings found:")
		for missing in missing_mappings:
			print("   - %s" % missing)
	else:
		print("✅ All teleport mappings are complete!")
	
	print("📈 Validation complete: %d mappings checked\n" % (maps.size() * (maps.size() - 1)))

func get_accurate_spawn_position(from_map: String, to_map: String) -> Vector2:
	"""Lấy vị trí spawn chính xác cho teleport từ map này sang map khác"""
	var mapping_key = from_map + "_to_" + to_map
	
	if position_mappings.has(mapping_key):
		var position = position_mappings[mapping_key]
		print("🎯 Accurate spawn position for %s: %s" % [mapping_key, position])
		return position
	else:
		print("⚠️ No mapping found for %s, using default" % mapping_key)
		return get_default_spawn_position(to_map)

func get_default_spawn_position(map_name: String) -> Vector2:
	"""Vị trí spawn mặc định cho từng map"""
	var default_positions = {
		"lang_van_lang": Vector2(300, -1900),
		"rung_nuong": Vector2(753, -1225),
		"dong_dau": Vector2(-1421, -429),
		"hang_an": Vector2(-2069, 484),
		"suoi_thieng": Vector2(-2069, 484),
		"doi_tre": Vector2(-2292, -538)
	}
	
	if default_positions.has(map_name):
		return default_positions[map_name]
	else:
		print("⚠️ No default position for map: %s" % map_name)
		return Vector2(0, 0)

func validate_position(position: Vector2) -> bool:
	"""Kiểm tra vị trí có hợp lệ không"""
	return position != Vector2.ZERO

func add_custom_mapping(from_map: String, to_map: String, position: Vector2):
	"""Thêm mapping tùy chỉnh"""
	var key = from_map + "_to_" + to_map
	position_mappings[key] = position
	print("📍 Added custom mapping: %s -> %s" % [key, position])

func list_all_mappings():
	"""Debug: Liệt kê tất cả mappings"""
	print("\n🗺️ All Position Mappings:")
	for key in position_mappings.keys():
		print("   %s: %s" % [key, position_mappings[key]])
	print("📊 Total: %d mappings\n" % position_mappings.size())

func debug_route(from_map: String, to_map: String):
	"""Debug thông tin cho một route cụ thể"""
	var mapping_key = from_map + "_to_" + to_map
	print("\n🔍 DEBUG ROUTE: %s → %s" % [from_map, to_map])
	print("   Mapping key: %s" % mapping_key)
	
	if position_mappings.has(mapping_key):
		print("   ✅ Position: %s" % position_mappings[mapping_key])
	else:
		print("   ❌ No mapping found!")
		var default_pos = get_default_spawn_position(to_map)
		print("   🏠 Default position: %s" % default_pos)
	print("")

func get_all_routes_from_map(map_name: String) -> Dictionary:
	"""Lấy tất cả routes từ một map cụ thể"""
	var routes = {}
	var prefix = map_name + "_to_"
	
	for key in position_mappings.keys():
		if key.begins_with(prefix):
			var target_map = key.replace(prefix, "")
			routes[target_map] = position_mappings[key]
	
	return routes

func get_all_routes_to_map(map_name: String) -> Dictionary:
	"""Lấy tất cả routes đến một map cụ thể"""
	var routes = {}
	var suffix = "_to_" + map_name
	
	for key in position_mappings.keys():
		if key.ends_with(suffix):
			var source_map = key.replace(suffix, "")
			routes[source_map] = position_mappings[key]
	
	return routes
