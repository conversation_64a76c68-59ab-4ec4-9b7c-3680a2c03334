# Comprehensive Teleport System Fix
# Sửa lỗi teleport dong_dau → doi_tre và các vấn đề liên quan
extends Node2D

func _ready():
	print("🔧 === COMPREHENSIVE TELEPORT SYSTEM FIX ===")
	
	# Wait for systems to load
	await get_tree().process_frame
	await get_tree().process_frame
	
	fix_teleport_system()
	validate_all_teleport_gates()
	fix_map_name_ui()
	test_specific_teleport_issue()

func fix_teleport_system():
	print("\n🔧 1. FIXING TELEPORT SYSTEM...")
	
	# Check TeleportPositionMapping
	if TeleportPositionMapping:
		print("✅ TeleportPositionMapping available")
		
		# Fix dong_dau → doi_tre mapping
		var current_mapping = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
		print("🎯 Current dong_dau → doi_tre: %s" % current_mapping)
		
		# Ensure the mapping is correct
		if current_mapping == Vector2(-2292, -538):
			print("✅ Position mapping is correct")
		else:
			print("🔧 Fixing position mapping...")
			TeleportPositionMapping.add_custom_mapping("dong_dau", "doi_tre", Vector2(-2292, -538))
	else:
		print("❌ TeleportPositionMapping not found!")

func validate_all_teleport_gates():
	print("\n🔧 2. VALIDATING ALL TELEPORT GATES...")
	
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates" % gates.size())
	
	for gate in gates:
		if gate is TeleportGate:
			print("🚪 Validating gate: %s" % gate.gate_id)
			print("  - Target scene: %s" % gate.target_scene)
			print("  - Target position: %s" % gate.target_position)
			
			# Check if target scene exists
			if not gate.target_scene.is_empty():
				if ResourceLoader.exists(gate.target_scene):
					print("  ✅ Target scene exists")
				else:
					print("  ❌ Target scene NOT found: %s" % gate.target_scene)
			else:
				print("  ⚠️ No target scene set")

func fix_map_name_ui():
	print("\n🔧 3. FIXING MAP NAME UI...")
	
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI available")
		
		# Force recreate UI if needed
		if not GlobalMapNameUI.is_map_name_visible():
			print("🔧 Recreating map name UI...")
			GlobalMapNameUI._create_persistent_map_ui()
		
		# Set current map name
		var current_scene = get_tree().current_scene
		if current_scene:
			var map_name = _extract_map_name_from_scene_path(current_scene.scene_file_path)
			if not map_name.is_empty():
				GlobalMapNameUI.set_map_name(map_name)
				print("🗺️ Set map name to: %s" % map_name)
		
		# Debug UI status
		GlobalMapNameUI.debug_ui_info()
	else:
		print("❌ GlobalMapNameUI not available!")

func test_specific_teleport_issue():
	print("\n🔧 4. TESTING DONG_DAU → DOI_TRE TELEPORT...")
	
	# Test position mapping
	if TeleportPositionMapping:
		var spawn_pos = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
		print("🎯 Spawn position for dong_dau → doi_tre: %s" % spawn_pos)
		
		# Validate spawn position is safe
		if spawn_pos != Vector2.ZERO:
			print("✅ Valid spawn position")
		else:
			print("❌ Invalid spawn position - using default")
			spawn_pos = TeleportPositionMapping.get_default_spawn_position("doi_tre")
			print("🔄 Default spawn position: %s" % spawn_pos)
	
	# Test scene loading
	var doi_tre_scene_path = "res://maps/doi_tre/scenes/doi_tre.tscn"
	if ResourceLoader.exists(doi_tre_scene_path):
		print("✅ doi_tre.tscn exists and can be loaded")
		
		# Test load
		var test_resource = ResourceLoader.load(doi_tre_scene_path)
		if test_resource:
			print("✅ doi_tre.tscn loads successfully")
		else:
			print("❌ Failed to load doi_tre.tscn")
	else:
		print("❌ doi_tre.tscn NOT found!")

func _extract_map_name_from_scene_path(scene_path: String) -> String:
	"""Extract map name from scene path"""
	if scene_path.is_empty():
		return ""
	
	var scene_name = scene_path.get_file().get_basename()
	var map_names = ["lang_van_lang", "dong_dau", "doi_tre", "hang_an", "rung_nuong", "suoi_thieng"]
	
	for map_name in map_names:
		if scene_name.contains(map_name):
			# Format proper map name
			match map_name:
				"lang_van_lang":
					return "Làng Văn Lang"
				"dong_dau":
					return "Đồng Đậu"
				"doi_tre":
					return "Đồi Tre"
				"hang_an":
					return "Hang Ăn"
				"rung_nuong":
					return "Rừng Nướng"
				"suoi_thieng":
					return "Suối Thiêng"
	
	return scene_name

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Running teleport system diagnostics...")
		validate_all_teleport_gates()
		fix_map_name_ui()
