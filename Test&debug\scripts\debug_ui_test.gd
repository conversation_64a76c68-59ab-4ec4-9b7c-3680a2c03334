# Debug script để test UI tên bản đồ
extends Node

func _ready():
	# Chờ một chút để các autoload khởi tạo
	await get_tree().process_frame
	await get_tree().process_frame
	
	print("🔍 Debug UI Test started...")
	
	# <PERSON><PERSON><PERSON> tra GlobalMapNameUI
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI found")
		GlobalMapNameUI.debug_ui_info()
	else:
		print("❌ GlobalMapNameUI not found")
	
	# Test cập nhật tên map
	if GlobalMapNameUI:
		print("🧪 Testing map name update...")
		GlobalMapNameUI.set_map_name("Test Map - Debug")
		
		# Debug sau khi update
		print("📊 After update:")
		GlobalMapNameUI.debug_ui_info()
		
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("🧪 Manual UI test triggered...")
		if GlobalMapNameUI:
			GlobalMapNameUI.set_map_name("Manual Test - " + str(Time.get_unix_time_from_system()))
			GlobalMapNameUI.debug_ui_info()
