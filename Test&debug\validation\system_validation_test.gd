# System Validation Test Script
# Comprehensive test cho UI và Teleport systems
extends Node

func _ready():
	print("🧪 === COMPREHENSIVE SYSTEM VALIDATION ===")
	call_deferred("validate_all_systems")

func validate_all_systems():
	await get_tree().process_frame
	
	print("📋 Testing System Components...")
	
	# Test 1: Autoload Systems
	test_autoload_systems()
	
	# Test 2: GlobalMapNameUI System
	test_global_map_name_ui()
	
	# Test 3: TeleportPositionMapping System
	test_teleport_position_mapping()
	
	# Test 4: Input Mapping
	test_input_mapping()
	
	# Test 5: Map Controllers
	await test_map_controllers()
	
	print("🎯 === VALIDATION COMPLETED ===")

func test_autoload_systems():
	print("\n🔧 Testing Autoload Systems...")
	
	# Check GlobalMapNameUI
	if GlobalMapNameUI:
		print("✅ GlobalMapNameUI autoload available")
		if GlobalMapNameUI.has_method("set_map_name"):
			print("✅ GlobalMapNameUI.set_map_name() method exists")
		else:
			print("❌ GlobalMapNameUI.set_map_name() method missing")
	else:
		print("❌ GlobalMapNameUI autoload NOT available")
	
	# Check TeleportPositionMapping
	if TeleportPositionMapping:
		print("✅ TeleportPositionMapping autoload available")
		if TeleportPositionMapping.has_method("get_accurate_spawn_position"):
			print("✅ TeleportPositionMapping.get_accurate_spawn_position() method exists")
		else:
			print("❌ TeleportPositionMapping.get_accurate_spawn_position() method missing")
	else:
		print("❌ TeleportPositionMapping autoload NOT available")
	
	# Check SceneManager
	if SceneManager:
		print("✅ SceneManager autoload available")
	else:
		print("❌ SceneManager autoload NOT available")

func test_global_map_name_ui():
	print("\n🗺️ Testing GlobalMapNameUI System...")
	
	if GlobalMapNameUI:
		# Test setting map name
		GlobalMapNameUI.set_map_name("Test Map")
		print("✅ GlobalMapNameUI.set_map_name() called successfully")
		
		# Check if UI is visible
		if GlobalMapNameUI.is_map_name_visible():
			print("✅ Map name UI is visible")
		else:
			print("❌ Map name UI is NOT visible")
		
		# Test current map name
		var current_name = GlobalMapNameUI.get_current_map_name()
		if current_name == "Test Map":
			print("✅ Map name correctly stored: %s" % current_name)
		else:
			print("❌ Map name incorrectly stored: %s" % current_name)
	else:
		print("❌ Cannot test GlobalMapNameUI - not available")

func test_teleport_position_mapping():
	print("\n🎯 Testing TeleportPositionMapping System...")
	
	if TeleportPositionMapping:
		# Test known position mapping
		var test_position = TeleportPositionMapping.get_accurate_spawn_position("lang_van_lang", "rung_nuong")
		if test_position != Vector2.ZERO:
			print("✅ Position mapping working: lang_van_lang_to_rung_nuong = %s" % test_position)
		else:
			print("❌ Position mapping failed for lang_van_lang_to_rung_nuong")
		
		# Test fallback position
		var fallback_position = TeleportPositionMapping.get_accurate_spawn_position("invalid_map", "invalid_target")
		print("✅ Fallback position: %s" % fallback_position)
		
		# Test multiple mappings
		var mappings_to_test = [
			["lang_van_lang", "dong_dau"],
			["rung_nuong", "hang_an"],
			["dong_dau", "doi_tre"]
		]
		
		for mapping in mappings_to_test:
			var pos = TeleportPositionMapping.get_accurate_spawn_position(mapping[0], mapping[1])
			if pos != Vector2.ZERO:
				print("✅ %s_to_%s: %s" % [mapping[0], mapping[1], pos])
			else:
				print("❌ Missing mapping: %s_to_%s" % [mapping[0], mapping[1]])
	else:
		print("❌ Cannot test TeleportPositionMapping - not available")

func test_input_mapping():
	print("\n⌨️ Testing Input Mapping...")
	
	# Check teleport_interact action
	if InputMap.has_action("teleport_interact"):
		print("✅ teleport_interact action exists")
		var events = InputMap.action_get_events("teleport_interact")
		var enter_found = false
		var m_found = false
		
		for event in events:
			if event is InputEventKey:
				if event.keycode == KEY_ENTER:
					enter_found = true
					print("✅ Enter key correctly mapped")
				elif event.keycode == KEY_M:
					m_found = true
					print("⚠️ WARNING: M key still mapped!")
		
		if not enter_found:
			print("❌ Enter key NOT mapped")
		if m_found:
			print("❌ Old M key mapping still exists")
		
	else:
		print("❌ teleport_interact action not found")

func test_map_controllers():
	print("\n🗺️ Testing Map Controllers...")
	
	# Test if map controllers are using GlobalMapNameUI
	var map_controller_paths = [
		"res://maps/lang_van_lang/scripts/lang_van_lang_map_controller.gd",
		"res://maps/dong_dau/scripts/dong_dau_map_controller.gd",
		"res://maps/hang_an/scripts/hang_an_map_controller.gd",
		"res://maps/doi_tre/scripts/doi_tre_map_controller.gd",
		"res://maps/rung_nuong/scripts/rung_nuong_map_controller.gd",
		"res://maps/suoi_thieng/scripts/suoi_thieng_map_controller.gd"
	]
	
	for controller_path in map_controller_paths:
		if ResourceLoader.exists(controller_path):
			print("✅ Map controller exists: %s" % controller_path.get_file())
		else:
			print("❌ Map controller missing: %s" % controller_path.get_file())
	
	# Simulate map controller behavior
	if GlobalMapNameUI:
		print("🧪 Simulating map controller behavior...")
		var test_maps = [
			"Làng Văn Lang",
			"Đồng Đậu", 
			"Hang Ăn",
			"Đồi Tre",
			"Rừng Nướng",
			"Suối Thiêng"
		]
		
		for map_name in test_maps:
			GlobalMapNameUI.set_map_name(map_name)
			await get_tree().process_frame
			print("✅ Map name updated to: %s" % map_name)
		
		print("✅ Map controller simulation completed")

# Helper function to simulate teleport
func simulate_teleport(from_map: String, to_map: String):
	print("\n🚀 Simulating teleport: %s -> %s" % [from_map, to_map])
	
	if TeleportPositionMapping:
		var spawn_pos = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		print("🎯 Spawn position: %s" % spawn_pos)
		
		if SceneManager:
			SceneManager.set_next_spawn_position(spawn_pos)
			print("✅ Spawn position set in SceneManager")
		
		if GlobalMapNameUI:
			GlobalMapNameUI.set_map_name(to_map.capitalize())
			print("✅ Map name updated")
	
	print("✅ Teleport simulation completed")
