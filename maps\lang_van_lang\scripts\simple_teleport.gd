# simple_teleport.gd - <PERSON>ript quản lý cổng dịch chuyển cho Làng Văn Lang với phím M
extends Node2D
class_name LangVanLangTeleportManager

# 🎯 Configuration cho cổng dịch chuyển
var gate_configs = {
	"left_gate": {
		"position": Vector2(300, -1900),
		"target_scene": "res://maps/dong_dau/scenes/dong_dau.tscn",
		"spawn_position": Vector2(3500, -1900),  # Spawn bên phải của Dong Da<PERSON>
		"gate_id": "lang_van_lang_to_dong_dau",
		"display_name": "🏛️ Đông Đầu",
		"description": "Thành phố cổ với kiến trúc truyền thống",
		"theme": "earth"
	},
	"right_gate": {
		"position": Vector2(3700, -1900),
		"target_scene": "res://maps/hang_an/scenes/hang_an.tscn",
		"spawn_position": Vector2(300, -1900),   # Spawn bên trái của Hang An
		"gate_id": "lang_van_lang_to_hang_an",
		"display_name": "🏔️ Hang Ăn",
		"description": "Hang động bí ẩn với nhiều kho báu",
		"theme": "water"
	}
}

# 📡 Variables
var teleport_gates: Array[TeleportGate] = []
var player: Player
var ui_container: Control

func _ready() -> void:
	print("🚀 === LANG VAN LANG TELEPORT SYSTEM KHỞI TẠO ===")
	
	# Thiết lập UI container
	_setup_ui_container()
	
	# Tìm player trong scene
	call_deferred("_find_player")
	call_deferred("_setup_teleport_gates")
	call_deferred("_setup_input_actions")

func _setup_ui_container() -> void:
	"""Thiết lập container cho UI"""
	ui_container = Control.new()
	ui_container.name = "TeleportUI"
	ui_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	get_tree().current_scene.add_child(ui_container)

func _setup_input_actions() -> void:
	"""Thiết lập phím tắt cho teleport"""
	if not InputMap.has_action("teleport_interact"):
		InputMap.add_action("teleport_interact")
		var event = InputEventKey.new()
		event.keycode = KEY_ENTER
		InputMap.action_add_event("teleport_interact", event)
		print("⌨️ Đã thiết lập phím Enter cho dịch chuyển")

func _find_player() -> void:
	"""Tìm player từ nhiều nguồn khác nhau"""
	# Method 1: Group
	player = get_tree().get_first_node_in_group("player")
	
	# Method 2: Relative path
	if not player:
		player = get_node_or_null("../Player")
	
	# Method 3: Find child
	if not player:
		player = get_tree().current_scene.find_child("Player", true, false)
	
	# Method 4: Search all nodes
	if not player:
		var all_nodes = get_tree().get_nodes_in_group("all")
		for node in all_nodes:
			if node is Player:
				player = node
				break
	
	if player:
		print("👤 Tìm thấy player tại %s" % player.global_position)
		# Kết nối signal nếu cần
		if player.has_signal("position_changed"):
			player.position_changed.connect(_on_player_position_changed)
	else:
		print("⚠️ CẢNH BÁO - Không tìm thấy player!")
		push_warning("LangVanLang Teleport: Không tìm thấy player trong scene")

func _setup_teleport_gates() -> void:
	print("🌀 Đang thiết lập cổng dịch chuyển...")
	
	# Xóa các cổng cũ nếu có
	_clear_existing_gates()
	
	# Tạo cổng mới với hiệu ứng
	for gate_id in gate_configs:
		var config = gate_configs[gate_id]
		_create_teleport_gate(gate_id, config)
		
		# Delay nhỏ giữa các cổng để tạo hiệu ứng
		await get_tree().create_timer(0.2).timeout
	
	print("✅ Hoàn tất thiết lập với %d cổng dịch chuyển" % teleport_gates.size())
	_show_welcome_message()

func _show_welcome_message() -> void:
	"""Hiển thị thông báo chào mừng"""
	if not ui_container:
		return
	
	var welcome_label = Label.new()
	welcome_label.text = "🏛️ Chào mừng đến Làng Văn Lang! Nhấn Enter gần cổng để di chuyển"
	welcome_label.position = Vector2(50, 50)
	welcome_label.size = Vector2(500, 30)
	welcome_label.add_theme_color_override("font_color", Color.YELLOW)
	ui_container.add_child.call_deferred(welcome_label)
	
	# Tự động ẩn sau 5 giây
	await get_tree().create_timer(5.0).timeout
	var tween = create_tween()
	tween.tween_property(welcome_label, "modulate:a", 0.0, 1.0)
	tween.tween_callback(welcome_label.queue_free)

func _clear_existing_gates() -> void:
	# Xóa các cổng cũ
	if is_instance_valid(self):
		for child in get_children():
			if child is TeleportGate:
				child.queue_free()
	
	teleport_gates.clear()

func _create_teleport_gate(gate_id: String, config: Dictionary) -> void:
	"""Tạo cổng dịch chuyển với cấu hình chi tiết"""
	# Load TeleportGate scene
	var gate_scene = preload("res://maps/lang_van_lang/scenes/TeleportGate.tscn")
	if not gate_scene:
		push_error("❌ Không thể load TeleportGate.tscn")
		return
	
	# Instantiate gate
	var gate = gate_scene.instantiate() as TeleportGate
	if not gate:
		push_error("❌ Không thể tạo TeleportGate")
		return
	
	# Configure gate
	gate.name = gate_id.capitalize().replace("_", "") + "Gate"
	gate.global_position = config.position
	gate.target_scene = config.target_scene
	gate.target_position = config.spawn_position
	gate.gate_id = config.gate_id
	gate.auto_teleport = false  # Luôn sử dụng phím Enter
	gate.interaction_key = "teleport_interact"
	print("[DEBUG] Created gate %s: auto_teleport=%s, interaction_key=%s, monitoring=%s" % [gate.gate_id, gate.auto_teleport, gate.interaction_key, gate.monitoring])
	
	# Thiết lập theme nếu có
	if config.has("theme"):
		gate.set_gate_theme(config.theme)
	else:
		# Visual configuration theo vị trí
		if gate_id == "left_gate":
			gate.gate_color = Color(0.8, 0.3, 0.3, 0.7)  # Đỏ cho cổng trái
		elif gate_id == "right_gate":
			gate.gate_color = Color(0.3, 0.8, 0.3, 0.7)  # Xanh cho cổng phải
	
	# Kết nối signals
	gate.player_entered_gate.connect(_on_player_entered_gate)
	gate.player_exited_gate.connect(_on_player_exited_gate)
	
	# Thêm vào scene với hiệu ứng
	add_child(gate)
	teleport_gates.append(gate)
	
	# Hiệu ứng xuất hiện
	gate.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(gate, "modulate:a", 1.0, 0.5)
	
	print("🌟 Đã tạo cổng '%s' tại %s -> %s (%s)" % [
		gate_id, 
		config.position, 
		config.target_scene, 
		config.get("display_name", "Unknown")
	])

func _on_player_entered_gate(gate: TeleportGate) -> void:
	"""Xử lý khi player vào cổng"""
	print("🚪 Player vào cổng %s" % gate.gate_id)
	
	# Tìm thông tin destination
	var destination_info = _get_destination_info(gate.gate_id)
	if destination_info:
		_show_gate_info(destination_info)

func _get_destination_info(gate_id: String) -> Dictionary:
	"""Lấy thông tin điểm đến từ gate_id"""
	for config_id in gate_configs:
		var config = gate_configs[config_id]
		if config.gate_id == gate_id:
			return config
	return {}

func _show_gate_info(destination_info: Dictionary) -> void:
	"""Hiển thị thông tin về điểm đến"""
	if not ui_container:
		return
	
	# Xóa thông báo cũ nếu có
	var old_info = ui_container.get_node_or_null("GateInfo")
	if old_info:
		old_info.queue_free()
	
	# Tạo panel thông tin
	var info_panel = Panel.new()
	info_panel.name = "GateInfo"
	info_panel.size = Vector2(300, 80)
	info_panel.position = Vector2(50, 100)
	
	# Tạo label thông tin
	var info_label = RichTextLabel.new()
	info_label.bbcode_enabled = true
	info_label.fit_content = true
	info_label.size = Vector2(280, 60)
	info_label.position = Vector2(10, 10)
	
	var text = "[center][color=yellow]%s[/color][/center]\n" % destination_info.get("display_name", "")
	text += "[center]%s[/center]\n" % destination_info.get("description", "")
	text += "[center][color=cyan]Nhấn [Enter] để dịch chuyển[/color][/center]"
	
	info_label.text = text
	info_panel.add_child(info_label)
	ui_container.add_child(info_panel)
	
	# Hiệu ứng xuất hiện
	info_panel.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(info_panel, "modulate:a", 1.0, 0.3)

func _on_player_exited_gate(gate: TeleportGate) -> void:
	"""Xử lý khi player ra khỏi cổng"""
	print("🚪 Player ra khỏi cổng %s" % gate.gate_id)
	
	# Ẩn thông tin cổng
	_hide_gate_info()

func _hide_gate_info() -> void:
	"""Ẩn thông tin cổng"""
	if not ui_container:
		return
	
	var info_panel = ui_container.get_node_or_null("GateInfo")
	if info_panel:
		var tween = create_tween()
		tween.tween_property(info_panel, "modulate:a", 0.0, 0.3)
		tween.tween_callback(info_panel.queue_free)

func _on_player_position_changed(new_position: Vector2) -> void:
	"""Xử lý khi player thay đổi vị trí"""
	# Có thể thêm logic kiểm tra khoảng cách đến cổng
	pass

# 🛠️ Testing Functions
func test_teleport_to_dong_dau() -> void:
	"""Test dịch chuyển đến Đông Đầu"""
	_force_teleport_to(
		"res://maps/dong_dau/scenes/dong_dau.tscn",
		Vector2(3500, -1900),
		"🏛️ Đông Đầu"
	)

func test_teleport_to_hang_an() -> void:
	"""Test dịch chuyển đến Hang Ăn"""
	_force_teleport_to(
		"res://maps/hang_an/scenes/hang_an.tscn",
		Vector2(300, -1900),
		"🏔️ Hang Ăn"
	)

func _force_teleport_to(target_scene: String, spawn_position: Vector2, destination_name: String = "") -> void:
	"""Force teleport player to a specific location (for testing)"""
	if not player:
		print("❌ Không thể force teleport - không tìm thấy player")
		return
	
	print("🚀 Force teleport đến %s" % destination_name)
	
	# Validate scene exists
	if not FileAccess.file_exists(target_scene):
		print("❌ Scene không tồn tại: %s" % target_scene)
		return
	
	# Save player state
	# if "GameData" in ProjectSettings.get_setting("autoload").keys() and GameData:
	# 	GameData.save_player_state(player)
	# 	GameData.set_next_spawn_position(spawn_position)
	# 	print("💾 Đã lưu player state")
	
	# Show transition effect
	_show_force_teleport_effect()
	
	# Change scene with delay
	await get_tree().create_timer(1.0).timeout
	
	print("✨ Dịch chuyển đến scene: %s" % target_scene)
	print("🔍 Kiểm tra SceneManager: %s" % SceneManager)
	if SceneManager:
		print("🔍 SceneManager có goto_scene method: %s" % SceneManager.has_method("goto_scene"))
	else:
		print("🔍 SceneManager không tồn tại")
	
	# Always use SceneManager if available to show loading screen
	if SceneManager and SceneManager.has_method("goto_scene"):
		print("🔄 Sử dụng SceneManager với loading screen")
		SceneManager.goto_scene(target_scene)
	else:
		print("⚠️ SceneManager không có sẵn, dùng phương thức thay thế")
		get_tree().change_scene_to_file(target_scene)

func _show_force_teleport_effect() -> void:
	"""Hiển thị hiệu ứng force teleport"""
	if not ui_container:
		return
	
	var effect_label = Label.new()
	effect_label.text = "⚡ FORCE TELEPORT ⚡"
	effect_label.position = Vector2(200, 300)
	effect_label.size = Vector2(200, 50)
	effect_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	effect_label.add_theme_color_override("font_color", Color.YELLOW)
	ui_container.add_child(effect_label)
	
	# Hiệu ứng nhấp nháy
	var tween = create_tween()
	tween.set_loops(3)
	tween.tween_property(effect_label, "modulate:a", 0.0, 0.3)
	tween.tween_property(effect_label, "modulate:a", 1.0, 0.3)
	tween.tween_callback(effect_label.queue_free)

# 🔍 Debug Functions
func debug_teleport_system() -> void:
	print("🔍 === LANG VAN LANG TELEPORT DEBUG INFO ===")
	print("👤 Player: %s" % (player != null))
	if player:
		print("   Vị trí: %s" % player.global_position)
	print("🌀 Số cổng: %d" % teleport_gates.size())
	
	for i in range(teleport_gates.size()):
		var gate = teleport_gates[i]
		print("   Cổng %d: %s" % [i+1, gate.gate_id])
		print("     Vị trí: %s" % gate.global_position)
		print("     Đích: %s" % gate.target_scene)
		print("     Hoạt động: %s" % gate.monitoring)
		if gate.has_method("debug_gate_info"):
			gate.debug_gate_info()
	
	print("🔍 === KẾT THÚC DEBUG INFO ===")

# 🎮 Management Functions
func enable_gate(gate_id: String) -> void:
	"""Bật một cổng cụ thể"""
	for gate in teleport_gates:
		if gate.gate_id.ends_with(gate_id):
			gate.enable_gate()
			print("✅ Đã bật cổng %s" % gate_id)
			return
	
	print("❌ Không tìm thấy cổng %s" % gate_id)

func disable_gate(gate_id: String) -> void:
	"""Tắt một cổng cụ thể"""
	for gate in teleport_gates:
		if gate.gate_id.ends_with(gate_id):
			gate.disable_gate()
			print("🔒 Đã tắt cổng %s" % gate_id)
			return
	
	print("❌ Không tìm thấy cổng %s" % gate_id)

func enable_all_gates() -> void:
	"""Bật tất cả cổng"""
	for gate in teleport_gates:
		gate.enable_gate()
	print("✅ Đã bật tất cả %d cổng" % teleport_gates.size())

func disable_all_gates() -> void:
	"""Tắt tất cả cổng"""
	for gate in teleport_gates:
		gate.disable_gate()
	print("🔒 Đã tắt tất cả %d cổng" % teleport_gates.size())

# 📊 Information Functions
func get_gate_info(gate_id: String) -> Dictionary:
	"""Lấy thông tin chi tiết của một cổng"""
	for gate in teleport_gates:
		if gate.gate_id.ends_with(gate_id):
			return {
				"id": gate.gate_id,
				"position": gate.global_position,
				"target_scene": gate.target_scene,
				"target_position": gate.target_position,
				"is_enabled": gate.monitoring,
				"player_inside": gate.get_player_inside_state() if gate.has_method("get_player_inside_state") else false
			}
	return {}

func list_all_gates() -> Array:
	"""Liệt kê tất cả cổng"""
	var gate_list = []
	for gate in teleport_gates:
		gate_list.append({
			"name": gate.name,
			"id": gate.gate_id,
			"position": gate.global_position,
			"enabled": gate.monitoring
		})
	return gate_list

# ⌨️ Input Handling cho Testing và Debug
func _input(event: InputEvent) -> void:
	# Chỉ hoạt động trong debug mode
	if not OS.is_debug_build():
		return
	
	# Phím Enter - Debug system
	if event.is_action_pressed("ui_accept"):
		debug_teleport_system()
	
	# Left Arrow - Test teleport to Đông Đầu
	if event.is_action_pressed("ui_left"):
		print("🏛️ Testing teleport to Đông Đầu...")
		test_teleport_to_dong_dau()
	
	# Right Arrow - Test teleport to Hang Ăn
	if event.is_action_pressed("ui_right"):
		print("🏔️ Testing teleport to Hang Ăn...")
		test_teleport_to_hang_an()
	
	# Phím số để quản lý cổng
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				enable_gate("dong_dau")
			KEY_2:
				enable_gate("hang_an")
			KEY_3:
				enable_all_gates()
			KEY_0:
				disable_all_gates()
			KEY_F1:
				print("🎮 === DEBUG CONTROLS ===")
				print("Enter: Debug system info")
				print("← Arrow: Test teleport to Đông Đầu")
				print("→ Arrow: Test teleport to Hang Ăn")
				print("1: Enable Đông Đầu gate")
				print("2: Enable Hang Ăn gate")
				print("3: Enable all gates")
				print("0: Disable all gates")
				print("F1: Show this help")
				print("========================")

# 🏠 Cleanup
func _exit_tree() -> void:
	"""Cleanup khi script bị xóa"""
	if ui_container and is_instance_valid(ui_container):
		ui_container.queue_free()
	
	# Disconnect signals
	for gate in teleport_gates:
		if gate and is_instance_valid(gate):
			if gate.player_entered_gate.is_connected(_on_player_entered_gate):
				gate.player_entered_gate.disconnect(_on_player_entered_gate)
			if gate.player_exited_gate.is_connected(_on_player_exited_gate):
				gate.player_exited_gate.disconnect(_on_player_exited_gate)
	
	print("🧹 LangVanLang Teleport Manager cleaned up")
