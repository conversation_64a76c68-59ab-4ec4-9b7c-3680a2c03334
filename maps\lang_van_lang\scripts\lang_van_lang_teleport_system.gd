# LangVanLangTeleportSystem.gd - <PERSON><PERSON> thống cổng dịch chuyển hoàn chỉnh cho Làng <PERSON>
extends Node2D
class_name LangVanLangTeleportSystem

# 🎯 Cấu hình cổng dịch chuyển
var teleport_gates_config = {
	"dong_dau": {
		"position": Vector2(300, -1900),
		"target_scene": "res://maps/dong_dau/scenes/dong_dau.tscn",
		"target_position": Vector2(3500, -1900),
		"gate_id": "lang_van_lang_to_dong_dau",
		"display_name": "🏛️ Đông Đầu",
		"description": "Thành phố cổ với kiến trúc truyền thống",
		"gate_color": Color(0.8, 0.3, 0.3, 0.7),
		"activated_color": Color(1, 0.5, 0.5, 0.9)
	},
	"hang_an": {
		"position": Vector2(3700, -1900),
		"target_scene": "res://maps/hang_an/scenes/hang_an.tscn",
		"target_position": Vector2(300, -1900),
		"gate_id": "lang_van_lang_to_hang_an",
		"display_name": "🏔️ Hang Ăn",
		"description": "<PERSON> động bí <PERSON>n với nhiều kho báu",
		"gate_color": Color(0.5, 0.3, 0.9, 0.7),
		"activated_color": Color(0.7, 0.5, 1, 0.9)
	},
	"rung_nuong": {
		"position": Vector2(2000, -1900),
		"target_scene": "res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"target_position": Vector2(750, -1200),
		"gate_id": "lang_van_lang_to_rung_nuong",
		"display_name": "🌲 Rừng Nướng",
		"description": "Khu rừng rậm rạp với nhiều sinh vật",
		"gate_color": Color(0.3, 0.8, 0.3, 0.7),
		"activated_color": Color(0.5, 1, 0.5, 0.9)
	},
	"doi_tre": {
		"position": Vector2(1100, -1900),
		"target_scene": "res://maps/doi_tre/scenes/doi_tre.tscn",
		"target_position": Vector2(500, -200),
		"gate_id": "lang_van_lang_to_doi_tre",
		"display_name": "🎋 Đồi Tre",
		"description": "Đồi tre xanh mát với gió lộng",
		"gate_color": Color(0.6, 0.8, 0.3, 0.7),
		"activated_color": Color(0.8, 1, 0.5, 0.9)
	},
	"suoi_thieng": {
		"position": Vector2(2500, -1900),
		"target_scene": "res://maps/suoi_thieng/scenes/suoi_thieng.tscn",
		"target_position": Vector2(-2000, 200),
		"gate_id": "lang_van_lang_to_suoi_thieng",
		"display_name": "🌊 Suối Thiêng",
		"description": "Suối nước thiêng linh huyền bí",
		"gate_color": Color(0.2, 0.8, 0.9, 0.7),
		"activated_color": Color(0.4, 1, 1, 0.9)
	}
}

# 📡 Variables
var active_gates: Array[TeleportGate] = []
var player: Player
var map_controller: LangVanLangMapController

# 🎮 Signals
signal gate_activated(gate_id: String)
signal gate_deactivated(gate_id: String)

func _ready() -> void:
	print("🌀 === LANG VAN LANG TELEPORT SYSTEM KHỞI TẠO ===")
	call_deferred("_initialize_system")

func _initialize_system() -> void:
	# Tìm player
	_find_player()
	
	# Tìm map controller
	_find_map_controller()
	
	# Thiết lập các cổng dịch chuyển - delay để đảm bảo scene đã sẵn sàng
	await get_tree().process_frame
	await get_tree().process_frame
	_setup_teleport_gates()
	
	# Thiết lập input mapping
	_setup_input_mapping()
	
	print("✅ Hệ thống cổng dịch chuyển đã sẵn sàng với %d cổng" % active_gates.size())

func _find_player() -> void:
	player = get_tree().get_first_node_in_group("player")
	if not player:
		player = get_tree().root.find_child("Player", true, false)
	
	if player:
		print("👤 Tìm thấy player: %s" % player.name)
	else:
		print("⚠️ CẢNH BÁO: Không tìm thấy player!")

func _find_map_controller() -> void:
	map_controller = get_parent() as LangVanLangMapController
	if not map_controller:
		map_controller = get_tree().root.find_child("LangVanLang", true, false)
	
	if map_controller:
		print("🗺️ Tìm thấy map controller: %s" % map_controller.name)
	else:
		print("⚠️ CẢNH BÁO: Không tìm thấy map controller!")

func _setup_teleport_gates() -> void:
	print("🛠️ Đang thiết lập cổng dịch chuyển...")
	
	# Tìm các cổng đã có trong scene thay vì tạo mới
	_find_existing_gates()
	
	print("✅ Đã thiết lập %d cổng dịch chuyển" % active_gates.size())

func _find_existing_gates() -> void:
	"""Tìm và thiết lập các cổng đã có trong scene"""
	# Tìm tất cả cổng trong group teleport_gates
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	for gate in gates:
		if gate is TeleportGate:
			active_gates.append(gate)
			_connect_gate_signals(gate)
			_configure_existing_gate(gate)
			print("🌀 Đã kết nối cổng: %s" % gate.gate_id)

func _configure_existing_gate(gate: TeleportGate) -> void:
	"""Cấu hình lại cổng đã có"""
	# Đảm bảo các thiết lập cơ bản
	gate.auto_teleport = false
	gate.interaction_key = "teleport_activate"
	gate.activation_delay = 0.5
	
	# Tạo hiệu ứng spawn
	_create_spawn_effect(gate)

func _clear_existing_gates() -> void:
	"""Xóa tất cả cổng cũ (không sử dụng nữa)"""
	# Không cần xóa cổng vì chúng đã có trong scene
	active_gates.clear()
	print("🧹 Đã reset danh sách cổng")

# Removed _create_teleport_gate function - using existing scene gates instead

func _connect_gate_signals(gate: TeleportGate) -> void:
	"""Kết nối signals của cổng"""
	if not gate.player_entered_gate.is_connected(_on_player_entered_gate):
		gate.player_entered_gate.connect(_on_player_entered_gate)
	
	if not gate.player_exited_gate.is_connected(_on_player_exited_gate):
		gate.player_exited_gate.connect(_on_player_exited_gate)

func _disconnect_gate_signals(gate: TeleportGate) -> void:
	"""Ngắt kết nối signals của cổng"""
	if gate.player_entered_gate.is_connected(_on_player_entered_gate):
		gate.player_entered_gate.disconnect(_on_player_entered_gate)
	
	if gate.player_exited_gate.is_connected(_on_player_exited_gate):
		gate.player_exited_gate.disconnect(_on_player_exited_gate)

func _create_spawn_effect(gate: TeleportGate) -> void:
	"""Tạo hiệu ứng spawn cho cổng"""
	gate.modulate.a = 0.0
	gate.scale = Vector2(0.5, 0.5)
	
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(gate, "modulate:a", 1.0, 0.8)
	tween.tween_property(gate, "scale", Vector2(1.0, 1.0), 0.8)
	tween.tween_callback(_show_gate_welcome_message.bind(gate))

func _show_gate_welcome_message(gate: TeleportGate) -> void:
	"""Hiển thị thông báo chào mừng cho cổng"""
	if not player:
		return
	
	# Tìm config của cổng
	var config = _get_gate_config(gate.gate_id)
	if not config:
		return
	
	# Tạo label thông báo
	var welcome_label = Label.new()
	welcome_label.text = "✨ Cổng dịch chuyển đến %s đã được kích hoạt!" % config.display_name
	welcome_label.add_theme_color_override("font_color", Color.YELLOW)
	welcome_label.add_theme_font_size_override("font_size", 18)
	welcome_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	welcome_label.position = Vector2(-150, -50)
	welcome_label.size = Vector2(300, 30)
	welcome_label.modulate.a = 0.0
	
	gate.add_child(welcome_label)
	
	# Hiệu ứng fade in/out
	var tween = create_tween()
	tween.tween_property(welcome_label, "modulate:a", 1.0, 0.5)
	await tween.finished
	
	# Wait for 2 seconds
	await get_tree().create_timer(2.0).timeout
	
	# Fade out
	var fade_out_tween = create_tween()
	fade_out_tween.tween_property(welcome_label, "modulate:a", 0.0, 0.5)
	await fade_out_tween.finished
	welcome_label.queue_free()

func _setup_input_mapping() -> void:
	"""Thiết lập input mapping cho phím Space"""
	if not InputMap.has_action("teleport_activate"):
		InputMap.add_action("teleport_activate")
		var event = InputEventKey.new()
		event.keycode = KEY_SPACE
		InputMap.action_add_event("teleport_activate", event)
		print("⌨️ Đã thêm phím Space cho teleport")

func _get_gate_config(gate_id: String) -> Dictionary:
	"""Lấy config của cổng theo ID"""
	for key in teleport_gates_config:
		if teleport_gates_config[key].gate_id == gate_id:
			return teleport_gates_config[key]
	
	# Nếu không tìm thấy config, trả về config mặc định
	return {
		"display_name": gate_id.capitalize().replace("_", " "),
		"description": "Cổng dịch chuyển"
	}

# 📡 Signal Handlers
func _on_player_entered_gate(gate: TeleportGate) -> void:
	print("🚪 Player đã vào cổng: %s" % gate.gate_id)
	gate_activated.emit(gate.gate_id)
	
	# Thông báo cho map controller
	if map_controller:
		map_controller.teleport_gate_activated.emit(gate.gate_id)

func _on_player_exited_gate(gate: TeleportGate) -> void:
	print("🚶 Player đã rời cổng: %s" % gate.gate_id)
	gate_deactivated.emit(gate.gate_id)

# 🎮 Public Methods
func enable_gate(gate_id: String) -> void:
	"""Kích hoạt cổng theo ID"""
	var gate = get_gate_by_id(gate_id)
	if gate:
		gate.enable_gate()
		print("🟢 Đã kích hoạt cổng: %s" % gate_id)

func disable_gate(gate_id: String) -> void:
	"""Vô hiệu hóa cổng theo ID"""
	var gate = get_gate_by_id(gate_id)
	if gate:
		gate.disable_gate()
		print("🔴 Đã vô hiệu hóa cổng: %s" % gate_id)

func get_gate_by_id(gate_id: String) -> TeleportGate:
	"""Tìm cổng theo ID"""
	for gate in active_gates:
		if gate.gate_id == gate_id:
			return gate
	return null

func get_all_gates() -> Array[TeleportGate]:
	"""Lấy tất cả cổng"""
	return active_gates

func teleport_to_gate(gate_id: String) -> void:
	"""Dịch chuyển trực tiếp đến cổng (debug)"""
	var gate = get_gate_by_id(gate_id)
	if gate and player:
		player.global_position = gate.global_position
		print("🎯 Dịch chuyển player đến cổng: %s" % gate_id)

# 🔧 Debug Methods
func debug_info() -> void:
	"""Hiển thị thông tin debug"""
	print("🔍 === TELEPORT SYSTEM DEBUG INFO ===")
	print("👤 Player: %s" % (player != null))
	print("🗺️ Map Controller: %s" % (map_controller != null))
	print("🌀 Số cổng active: %d" % active_gates.size())
	
	for i in range(active_gates.size()):
		var gate = active_gates[i]
		print("   Cổng %d: %s (%s)" % [i+1, gate.name, gate.gate_id])
		print("     Vị trí: %s" % gate.global_position)
		print("     Đích: %s" % gate.target_scene)
		print("     Enabled: %s" % gate.monitoring)
	
	print("=====================================")

# 🎮 Input Handling (Debug)
func _input(event: InputEvent) -> void:
	# Chỉ hoạt động trong debug mode
	if not OS.is_debug_build():
		return
	
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				debug_info()
			KEY_F2:
				teleport_to_gate("lang_van_lang_to_dong_dau")
			KEY_F3:
				teleport_to_gate("lang_van_lang_to_hang_an")
			KEY_F4:
				teleport_to_gate("lang_van_lang_to_rung_nuong")
			KEY_F5:
				teleport_to_gate("lang_van_lang_to_doi_tre")
			KEY_F6:
				teleport_to_gate("lang_van_lang_to_suoi_thieng")

# 🧹 Cleanup
func _exit_tree() -> void:
	"""Cleanup khi thoát"""
	for gate in active_gates:
		if is_instance_valid(gate):
			_disconnect_gate_signals(gate)
	
	active_gates.clear()
	print("🧹 Teleport system đã cleanup")
